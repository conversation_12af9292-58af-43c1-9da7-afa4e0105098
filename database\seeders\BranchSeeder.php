<?php

namespace Database\Seeders;

use Bo<PERSON>ble\Base\Supports\BaseSeeder;
use Bo<PERSON>ble\BranchManagement\Models\Branch;
use Botble\Location\Models\City;

class BranchSeeder extends BaseSeeder
{
    public function run(): void
    {
        Branch::query()->truncate();

        $cities = City::query()->wherePublished()->limit(5)->get();

        if ($cities->isEmpty()) {
            $this->command->warn('No cities found. Please ensure the Location plugin is installed and has city data.');
            return;
        }

        $sampleBranches = [
            [
                'name' => 'Downtown Branch',
                'description' => 'Our main downtown location with full services',
                'address' => '123 Main Street, Downtown',
                'phone' => '******-0101',
                'email' => '<EMAIL>',
                'manager_name' => '<PERSON>',
                'manager_phone' => '******-0102',
                'manager_email' => '<EMAIL>',
                'zip_code' => '12345',
                'pickup_fee' => 0,
                'is_featured' => true,
                'is_pickup_available' => true,
                'facilities' => ['Parking', 'WiFi', 'Air Conditioning', 'Customer Lounge'],
                'special_instructions' => 'Please bring your order confirmation and ID for pickup.',
            ],
            [
                'name' => 'Mall Branch',
                'description' => 'Convenient location inside the shopping mall',
                'address' => '456 Shopping Mall, Level 2',
                'phone' => '******-0201',
                'email' => '<EMAIL>',
                'manager_name' => 'Sarah Johnson',
                'manager_phone' => '******-0202',
                'zip_code' => '12346',
                'pickup_fee' => 2.50,
                'is_featured' => false,
                'is_pickup_available' => true,
                'facilities' => ['Mall Parking', 'Food Court Nearby', 'Easy Access'],
                'special_instructions' => 'Located near the main entrance on Level 2.',
            ],
            [
                'name' => 'Warehouse Outlet',
                'description' => 'Large warehouse with bulk pickup options',
                'address' => '789 Industrial Drive',
                'phone' => '******-0301',
                'email' => '<EMAIL>',
                'zip_code' => '12347',
                'pickup_fee' => 0,
                'is_featured' => true,
                'is_pickup_available' => true,
                'facilities' => ['Large Parking', 'Loading Dock', 'Bulk Orders'],
                'special_instructions' => 'For large orders, please call ahead to arrange pickup time.',
            ],
        ];

        $operatingHours = [
            'monday' => ['is_open' => true, 'open' => '09:00', 'close' => '18:00'],
            'tuesday' => ['is_open' => true, 'open' => '09:00', 'close' => '18:00'],
            'wednesday' => ['is_open' => true, 'open' => '09:00', 'close' => '18:00'],
            'thursday' => ['is_open' => true, 'open' => '09:00', 'close' => '18:00'],
            'friday' => ['is_open' => true, 'open' => '09:00', 'close' => '18:00'],
            'saturday' => ['is_open' => true, 'open' => '10:00', 'close' => '16:00'],
            'sunday' => ['is_open' => false, 'open' => '00:00', 'close' => '00:00'],
        ];

        foreach ($sampleBranches as $index => $branchData) {
            $city = $cities->get($index % $cities->count());

            Branch::create(array_merge($branchData, [
                'city_id' => $city->id,
                'state_id' => $city->state_id,
                'country_id' => $city->country_id,
                'operating_hours' => $operatingHours,
                'status' => 'published',
                'order' => $index + 1,
            ]));
        }

        $this->command->info('Created ' . count($sampleBranches) . ' sample branches.');
    }
}
