{"__meta": {"id": "01K3S90C0EZ23Q90FTXWWQG00W", "datetime": "2025-08-28 21:23:12", "utime": **********.526899, "method": "POST", "uri": "/ajax/checkout/update", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[21:23:12] LOG.info: HyperPay API Service Config Loaded {\n    \"gateway_url\": \"https:\\/\\/eu-test.oppwa.com\",\n    \"currency\": \"SAR\",\n    \"has_access_token\": true,\n    \"has_entity_id\": true,\n    \"sandbox_mode\": \"1\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.471966, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 33, "start": 1756416191.53185, "end": **********.526915, "duration": 0.9950649738311768, "duration_str": "995ms", "measures": [{"label": "Booting", "start": 1756416191.53185, "relative_start": 0, "end": **********.263802, "relative_end": **********.263802, "duration": 0.****************, "duration_str": "732ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.263813, "relative_start": 0.****************, "end": **********.526917, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "263ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.281131, "relative_start": 0.****************, "end": **********.292345, "relative_end": **********.292345, "duration": 0.011214017868041992, "duration_str": "11.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/ecommerce::orders.partials.amount", "start": **********.407845, "relative_start": 0.****************, "end": **********.407845, "relative_end": **********.407845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::orders.checkout.products", "start": **********.410722, "relative_start": 0.****************, "end": **********.410722, "relative_end": **********.410722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::themes.includes.rating-star", "start": **********.426006, "relative_start": 0.8941559791564941, "end": **********.426006, "relative_end": **********.426006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.checkout.product", "start": **********.428838, "relative_start": 0.8969879150390625, "end": **********.428838, "relative_end": **********.428838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7df89dd2efa39b7a56dc8e528ce4f9d4", "start": **********.442176, "relative_start": 0.9103260040283203, "end": **********.442176, "relative_end": **********.442176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::baaf02f6c56c328f3c307011a60b6b9f", "start": **********.445075, "relative_start": 0.9132249355316162, "end": **********.445075, "relative_end": **********.445075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::themes.includes.cart-item-options-extras", "start": **********.44603, "relative_start": 0.914179801940918, "end": **********.44603, "relative_end": **********.44603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.partials.shipping-option", "start": **********.451558, "relative_start": 0.9197080135345459, "end": **********.451558, "relative_end": **********.451558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.partials.shipping-option", "start": **********.452643, "relative_start": 0.920792818069458, "end": **********.452643, "relative_end": **********.452643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.partials.payment-methods", "start": **********.455582, "relative_start": 0.923731803894043, "end": **********.455582, "relative_end": **********.455582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/stripe::methods", "start": **********.45726, "relative_start": 0.9254097938537598, "end": **********.45726, "relative_end": **********.45726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/paypal::methods", "start": **********.457755, "relative_start": 0.9259049892425537, "end": **********.457755, "relative_end": **********.457755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/hyperpay::methods", "start": **********.458191, "relative_start": 0.9263408184051514, "end": **********.458191, "relative_end": **********.458191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: fa081de1c3ee47622336b4eeafa01705::payment-method", "start": **********.474391, "relative_start": 0.9425408840179443, "end": **********.474391, "relative_end": **********.474391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/razorpay::methods", "start": **********.480526, "relative_start": 0.9486758708953857, "end": **********.480526, "relative_end": **********.480526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/cmi::methods", "start": **********.482363, "relative_start": 0.9505128860473633, "end": **********.482363, "relative_end": **********.482363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/paystack::methods", "start": **********.483374, "relative_start": 0.951524019241333, "end": **********.483374, "relative_end": **********.483374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/tabby::methods", "start": **********.488861, "relative_start": 0.9570109844207764, "end": **********.488861, "relative_end": **********.488861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: fa081de1c3ee47622336b4eeafa01705::payment-method", "start": **********.491456, "relative_start": 0.9596059322357178, "end": **********.491456, "relative_end": **********.491456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/sslcommerz::methods", "start": **********.496355, "relative_start": 0.9645049571990967, "end": **********.496355, "relative_end": **********.496355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/mollie::methods", "start": **********.497083, "relative_start": 0.9652328491210938, "end": **********.497083, "relative_end": **********.497083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/payment::partials.cod", "start": **********.497816, "relative_start": 0.965965986251831, "end": **********.497816, "relative_end": **********.497816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: fa081de1c3ee47622336b4eeafa01705::payment-method", "start": **********.499506, "relative_start": 0.****************, "end": **********.499506, "relative_end": **********.499506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/payment::partials.bank-transfer", "start": **********.503516, "relative_start": 0.****************, "end": **********.503516, "relative_end": **********.503516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: fa081de1c3ee47622336b4eeafa01705::payment-method", "start": **********.504571, "relative_start": 0.****************, "end": **********.504571, "relative_end": **********.504571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.partials.shipping-methods", "start": **********.519033, "relative_start": 0.****************, "end": **********.519033, "relative_end": **********.519033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.partials.shipping-option", "start": **********.519463, "relative_start": 0.9876129627227783, "end": **********.519463, "relative_end": **********.519463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.partials.shipping-option", "start": **********.520201, "relative_start": 0.9883508682250977, "end": **********.520201, "relative_end": **********.520201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.partials.checkout-button", "start": **********.520796, "relative_start": 0.9889459609985352, "end": **********.520796, "relative_end": **********.520796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.523188, "relative_start": 0.9913380146026611, "end": **********.524618, "relative_end": **********.524618, "duration": 0.0014297962188720703, "duration_str": "1.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 58473608, "peak_usage_str": "56MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 29, "nb_templates": 29, "templates": [{"name": "plugins/ecommerce::orders.partials.amount", "param_count": null, "params": [], "start": **********.40782, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/amount.blade.phpplugins/ecommerce::orders.partials.amount", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Famount.blade.php&line=1", "ajax": false, "filename": "amount.blade.php", "line": "?"}}, {"name": "plugins/marketplace::orders.checkout.products", "param_count": null, "params": [], "start": **********.410703, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/orders/checkout/products.blade.phpplugins/marketplace::orders.checkout.products", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Forders%2Fcheckout%2Fproducts.blade.php&line=1", "ajax": false, "filename": "products.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.rating-star", "param_count": null, "params": [], "start": **********.425986, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/themes/includes/rating-star.blade.phpplugins/ecommerce::themes.includes.rating-star", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Frating-star.blade.php&line=1", "ajax": false, "filename": "rating-star.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::orders.checkout.product", "param_count": null, "params": [], "start": **********.428798, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/checkout/product.blade.phpplugins/ecommerce::orders.checkout.product", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fcheckout%2Fproduct.blade.php&line=1", "ajax": false, "filename": "product.blade.php", "line": "?"}}, {"name": "__components::7df89dd2efa39b7a56dc8e528ce4f9d4", "param_count": null, "params": [], "start": **********.442155, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/7df89dd2efa39b7a56dc8e528ce4f9d4.blade.php__components::7df89dd2efa39b7a56dc8e528ce4f9d4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F7df89dd2efa39b7a56dc8e528ce4f9d4.blade.php&line=1", "ajax": false, "filename": "7df89dd2efa39b7a56dc8e528ce4f9d4.blade.php", "line": "?"}}, {"name": "__components::baaf02f6c56c328f3c307011a60b6b9f", "param_count": null, "params": [], "start": **********.445054, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/baaf02f6c56c328f3c307011a60b6b9f.blade.php__components::baaf02f6c56c328f3c307011a60b6b9f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fbaaf02f6c56c328f3c307011a60b6b9f.blade.php&line=1", "ajax": false, "filename": "baaf02f6c56c328f3c307011a60b6b9f.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.cart-item-options-extras", "param_count": null, "params": [], "start": **********.446009, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/themes/includes/cart-item-options-extras.blade.phpplugins/ecommerce::themes.includes.cart-item-options-extras", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fcart-item-options-extras.blade.php&line=1", "ajax": false, "filename": "cart-item-options-extras.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::orders.partials.shipping-option", "param_count": null, "params": [], "start": **********.451538, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/shipping-option.blade.phpplugins/ecommerce::orders.partials.shipping-option", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Fshipping-option.blade.php&line=1", "ajax": false, "filename": "shipping-option.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::orders.partials.shipping-option", "param_count": null, "params": [], "start": **********.452623, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/shipping-option.blade.phpplugins/ecommerce::orders.partials.shipping-option", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Fshipping-option.blade.php&line=1", "ajax": false, "filename": "shipping-option.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::orders.partials.payment-methods", "param_count": null, "params": [], "start": **********.455563, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/payment-methods.blade.phpplugins/ecommerce::orders.partials.payment-methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Fpayment-methods.blade.php&line=1", "ajax": false, "filename": "payment-methods.blade.php", "line": "?"}}, {"name": "plugins/stripe::methods", "param_count": null, "params": [], "start": **********.457241, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/stripe/resources/views/methods.blade.phpplugins/stripe::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fstripe%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}}, {"name": "plugins/paypal::methods", "param_count": null, "params": [], "start": **********.457737, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/paypal/resources/views/methods.blade.phpplugins/paypal::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpaypal%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}}, {"name": "plugins/hyperpay::methods", "param_count": null, "params": [], "start": **********.458173, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/hyperpay/resources/views/methods.blade.phpplugins/hyperpay::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fhyperpay%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}}, {"name": "fa081de1c3ee47622336b4eeafa01705::payment-method", "param_count": null, "params": [], "start": **********.474369, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/payment/resources/views/components/payment-method.blade.phpfa081de1c3ee47622336b4eeafa01705::payment-method", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fcomponents%2Fpayment-method.blade.php&line=1", "ajax": false, "filename": "payment-method.blade.php", "line": "?"}}, {"name": "plugins/razorpay::methods", "param_count": null, "params": [], "start": **********.480505, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/razorpay/resources/views/methods.blade.phpplugins/razorpay::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Frazorpay%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}}, {"name": "plugins/cmi::methods", "param_count": null, "params": [], "start": **********.482344, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/cmi/resources/views/methods.blade.phpplugins/cmi::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fcmi%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}}, {"name": "plugins/paystack::methods", "param_count": null, "params": [], "start": **********.483355, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/paystack/resources/views/methods.blade.phpplugins/paystack::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpaystack%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}}, {"name": "plugins/tabby::methods", "param_count": null, "params": [], "start": **********.488842, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/tabby/resources/views/methods.blade.phpplugins/tabby::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Ftabby%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}}, {"name": "fa081de1c3ee47622336b4eeafa01705::payment-method", "param_count": null, "params": [], "start": **********.491437, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/payment/resources/views/components/payment-method.blade.phpfa081de1c3ee47622336b4eeafa01705::payment-method", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fcomponents%2Fpayment-method.blade.php&line=1", "ajax": false, "filename": "payment-method.blade.php", "line": "?"}}, {"name": "plugins/sslcommerz::methods", "param_count": null, "params": [], "start": **********.496336, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/sslcommerz/resources/views/methods.blade.phpplugins/sslcommerz::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fsslcommerz%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}}, {"name": "plugins/mollie::methods", "param_count": null, "params": [], "start": **********.497065, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/mollie/resources/views/methods.blade.phpplugins/mollie::methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmollie%2Fresources%2Fviews%2Fmethods.blade.php&line=1", "ajax": false, "filename": "methods.blade.php", "line": "?"}}, {"name": "plugins/payment::partials.cod", "param_count": null, "params": [], "start": **********.497798, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/payment/resources/views/partials/cod.blade.phpplugins/payment::partials.cod", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fpartials%2Fcod.blade.php&line=1", "ajax": false, "filename": "cod.blade.php", "line": "?"}}, {"name": "fa081de1c3ee47622336b4eeafa01705::payment-method", "param_count": null, "params": [], "start": **********.499488, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/payment/resources/views/components/payment-method.blade.phpfa081de1c3ee47622336b4eeafa01705::payment-method", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fcomponents%2Fpayment-method.blade.php&line=1", "ajax": false, "filename": "payment-method.blade.php", "line": "?"}}, {"name": "plugins/payment::partials.bank-transfer", "param_count": null, "params": [], "start": **********.503497, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/payment/resources/views/partials/bank-transfer.blade.phpplugins/payment::partials.bank-transfer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fpartials%2Fbank-transfer.blade.php&line=1", "ajax": false, "filename": "bank-transfer.blade.php", "line": "?"}}, {"name": "fa081de1c3ee47622336b4eeafa01705::payment-method", "param_count": null, "params": [], "start": **********.504553, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/payment/resources/views/components/payment-method.blade.phpfa081de1c3ee47622336b4eeafa01705::payment-method", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fresources%2Fviews%2Fcomponents%2Fpayment-method.blade.php&line=1", "ajax": false, "filename": "payment-method.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::orders.partials.shipping-methods", "param_count": null, "params": [], "start": **********.519013, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/shipping-methods.blade.phpplugins/ecommerce::orders.partials.shipping-methods", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Fshipping-methods.blade.php&line=1", "ajax": false, "filename": "shipping-methods.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::orders.partials.shipping-option", "param_count": null, "params": [], "start": **********.519444, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/shipping-option.blade.phpplugins/ecommerce::orders.partials.shipping-option", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Fshipping-option.blade.php&line=1", "ajax": false, "filename": "shipping-option.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::orders.partials.shipping-option", "param_count": null, "params": [], "start": **********.520181, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/shipping-option.blade.phpplugins/ecommerce::orders.partials.shipping-option", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Fshipping-option.blade.php&line=1", "ajax": false, "filename": "shipping-option.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::orders.partials.checkout-button", "param_count": null, "params": [], "start": **********.520777, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/checkout-button.blade.phpplugins/ecommerce::orders.partials.checkout-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fpartials%2Fcheckout-button.blade.php&line=1", "ajax": false, "filename": "checkout-button.blade.php", "line": "?"}}]}, "queries": {"count": 23, "nb_statements": 23, "nb_visible_statements": 23, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01058, "accumulated_duration_str": "10.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.317872, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 4.537}, {"sql": "select distinct `ec_products`.*, `products_with_final_price`.`final_price`, `ec_products`.`with_storehouse_management`, `ec_products`.`stock_status`, `ec_products`.`quantity`, `ec_products`.`allow_checkout_when_out_of_stock` from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-08-28 21:23:12' OR\nec_products.end_date < '2025-08-28 21:23:12'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-28 21:23:12' AND\nec_products.end_date >= '2025-08-28 21:23:12'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-08-28 21:23:12'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-28 21:23:12' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `status` = 'published' and `ec_products`.`id` in (39) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) order by\nCASE\nWHEN ec_products.with_storehouse_management = 0 THEN\nCASE WHEN ec_products.stock_status = 'out_of_stock' THEN 1 ELSE 0 END\nELSE\nCASE WHEN ec_products.quantity <= 0 AND ec_products.allow_checkout_when_out_of_stock = 0 THEN 1 ELSE 0 END\nEND ASC\n, `order` asc, `created_at` desc", "type": "query", "params": [], "bindings": ["published", 39, 1, "published", "out_of_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 605}], "start": **********.3277369, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 4.537, "width_percent": 15.595}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (39) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.332987, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 20.132, "width_percent": 3.403}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (39)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.335972, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 23.535, "width_percent": 3.119}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (39)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 605}], "start": **********.3402429, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 26.654, "width_percent": 3.686}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (39)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 605}], "start": **********.341801, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 30.34, "width_percent": 3.497}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` in (39)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.343743, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 33.837, "width_percent": 3.214}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (7) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}], "start": **********.3461301, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 37.051, "width_percent": 4.631}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (7) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 34, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}], "start": **********.347526, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 41.682, "width_percent": 3.025}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 34, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}], "start": **********.348976, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 44.707, "width_percent": 3.592}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (3) and `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store'", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 39, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}], "start": **********.35011, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 48.299, "width_percent": 3.308}, {"sql": "select distinct `ec_product_variations`.`product_id`, `ec_product_variations`.`configurable_product_id`, `ec_product_attributes`.*, `ec_product_attribute_sets`.`title` as `attribute_set_title`, `ec_product_attribute_sets`.`slug` as `attribute_set_slug`, `ec_product_attribute_sets`.`order` as `attribute_set_order` from `ec_product_variations` inner join `ec_product_variation_items` on `ec_product_variation_items`.`variation_id` = `ec_product_variations`.`id` inner join `ec_product_attributes` on `ec_product_attributes`.`id` = `ec_product_variation_items`.`attribute_id` inner join `ec_product_attribute_sets` on `ec_product_attribute_sets`.`id` = `ec_product_attributes`.`attribute_set_id` where `ec_product_variations`.`product_id` in (39) order by `attribute_set_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.353296, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 51.607, "width_percent": 3.214}, {"sql": "select * from `ec_customers` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 250}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/PromotionCacheService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\PromotionCacheService.php", "line": 33}], "start": **********.365565, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 54.82, "width_percent": 3.403}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 597}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 210}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 668}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 594}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 834}], "start": **********.37974, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:597", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 597}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=597", "ajax": false, "filename": "EcommerceHelper.php", "line": "597"}, "connection": "martfury", "explain": null, "start_percent": 58.223, "width_percent": 3.025}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 598}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 210}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 668}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 594}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 834}], "start": **********.380959, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:598", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 598}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=598", "ajax": false, "filename": "EcommerceHelper.php", "line": "598"}, "connection": "martfury", "explain": null, "start_percent": 61.248, "width_percent": 2.552}, {"sql": "select `name`, `code` from `countries` where `status` = 'published' order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 216}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 668}, {"index": 19, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 594}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 834}], "start": **********.382034, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 63.8, "width_percent": 2.647}, {"sql": "select * from `ec_orders` where `id` = 76 limit 1", "type": "query", "params": [], "bindings": [76], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleCheckoutOrderData.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleCheckoutOrderData.php", "line": 60}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicUpdateCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicUpdateCheckoutController.php", "line": 29}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.395761, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 66.446, "width_percent": 7.94}, {"sql": "select `ec_taxes`.*, `ec_tax_products`.`product_id` as `pivot_product_id`, `ec_tax_products`.`tax_id` as `pivot_tax_id` from `ec_taxes` inner join `ec_tax_products` on `ec_taxes`.`id` = `ec_tax_products`.`tax_id` where `ec_tax_products`.`product_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/TaxRateCalculatorService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\TaxRateCalculatorService.php", "line": 19}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 56}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 43}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicUpdateCheckoutController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicUpdateCheckoutController.php", "line": 36}], "start": **********.4012, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "martfury", "explain": null, "start_percent": 74.386, "width_percent": 3.119}, {"sql": "select * from `ec_tax_rules` where `ec_tax_rules`.`tax_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 26, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/TaxRateCalculatorService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\TaxRateCalculatorService.php", "line": 19}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 56}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleTaxService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleTaxService.php", "line": 43}], "start": **********.403613, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 77.505, "width_percent": 2.741}, {"sql": "select avg(`star`) as aggregate from `ec_products` inner join `ec_reviews` on `ec_products`.`id` = `ec_reviews`.`product_id` where `ec_products`.`store_id` = 3 and `ec_products`.`store_id` is not null and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [3, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "plugins/marketplace::orders.checkout.products", "file": "D:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/orders/checkout/products.blade.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.4249048, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "marketplace::orders.checkout.products:43", "source": {"index": 19, "namespace": "view", "name": "plugins/marketplace::orders.checkout.products", "file": "D:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/orders/checkout/products.blade.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Forders%2Fcheckout%2Fproducts.blade.php&line=43", "ajax": false, "filename": "products.blade.php", "line": "43"}, "connection": "martfury", "explain": null, "start_percent": 80.246, "width_percent": 3.97}, {"sql": "select count(*) as aggregate from `ec_products` inner join `ec_reviews` on `ec_products`.`id` = `ec_reviews`.`product_id` where `ec_products`.`store_id` = 3 and `ec_products`.`store_id` is not null and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [3, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "plugins/marketplace::orders.checkout.products", "file": "D:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/orders/checkout/products.blade.php", "line": 45}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.427577, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "marketplace::orders.checkout.products:45", "source": {"index": 19, "namespace": "view", "name": "plugins/marketplace::orders.checkout.products", "file": "D:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/orders/checkout/products.blade.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Forders%2Fcheckout%2Fproducts.blade.php&line=45", "ajax": false, "filename": "products.blade.php", "line": "45"}, "connection": "martfury", "explain": null, "start_percent": 84.216, "width_percent": 2.93}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 39 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [39, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1002}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.448605, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 87.146, "width_percent": 5.293}, {"sql": "select `code` from `countries` where `id` = 'PE' limit 1", "type": "query", "params": [], "bindings": ["PE"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleSetCountryForPaymentCheckout.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleSetCountryForPaymentCheckout.php", "line": 18}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": "view", "name": "plugins/ecommerce::orders.partials.payment-methods", "file": "D:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/partials/payment-methods.blade.php", "line": 10}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.5099752, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 92.439, "width_percent": 7.561}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Currency": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Product": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductVariation": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1", "ajax": false, "filename": "ProductVariation.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Location\\Models\\Country": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Order": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Tax": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FTax.php&line=1", "ajax": false, "filename": "Tax.php", "line": "?"}}}, "count": 18, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 18}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://martfury.gc/ajax/checkout/update", "action_name": "public.ajax.checkout.update", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicUpdateCheckoutController@__invoke", "uri": "POST ajax/checkout/update", "controller": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicUpdateCheckoutController@__invoke<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicUpdateCheckoutController.php&line=18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers\\Fronts", "prefix": "/", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicUpdateCheckoutController.php&line=18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicUpdateCheckoutController.php:18-73</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect, Botble\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware", "duration": "996ms", "peak_memory": "58MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-352585752 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-352585752\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1784500849 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WR19pjDDCaSy1nFQfLF0llRCMr5qDSh1HZpER2CZ</span>\"\n  \"<span class=sf-dump-key>checkout-token</span>\" => \"<span class=sf-dump-str title=\"32 characters\">3562afa824d90efaece25a60fd1d50a8</span>\"\n  \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>db532641fa6fbd0ec8cd3cf8d0aef707</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>values</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>qty</span>\" => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>shipping_option</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>shipping_method</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => \"<span class=sf-dump-str title=\"13 characters\">branch_pickup</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>coupon_code</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>save-shipping-information-url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">https://martfury.gc/checkout/3562afa824d90efaece25a60fd1d50a8/information</span>\"\n  \"<span class=sf-dump-key>update-tax-url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">https://martfury.gc/ajax/checkout/update-tax</span>\"\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>address_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">SA</span>\"\n    \"<span class=sf-dump-key>state</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>city</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"5 characters\">671.2</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SAR</span>\"\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>customer_type</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Botble\\Ecommerce\\Models\\Customer</span>\"\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">cod</span>\"\n  \"<span class=sf-dump-key>hyperpay_payment_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">visa</span>\"\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>tax_information</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>company_name</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>company_address</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>company_tax_code</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>company_email</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784500849\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3206</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryyDsRZ3i8jleFhbvU</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"61 characters\">https://martfury.gc/checkout/3562afa824d90efaece25a60fd1d50a8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3264 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; ajs_anonymous_id=%22d13d80ea-7fe8-40be-a19b-193267ad504f%22; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; perf_dv6Tr4n=1; XSRF-TOKEN=eyJpdiI6Ik5KWk1jRTY0NUkrTG9obVJpZnVGSHc9PSIsInZhbHVlIjoiZTNVNys0RGI0eVVxcVVnSnhRLys5TzhQZFJhc2FGR2ErRGdCdzhoWkhHVmhjdmhuRDFVSTZkTzZaNjVYbkhZd045VXl6REdUYzkxVmh3eGpSeTNEaHN2QTVReGRXbXBOVHl1VGVISVhGNmxsY21LU1pRV1BQckVOK0RXK3doSEsiLCJtYWMiOiI1MTBmNTVmZDgyYzAzMWRlM2QyYzMxZjNjMmYwMzE3OTkxZmJkNGZiYTU2YWU1YWZmZTMwYmQxMjI1NWRmNmRkIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IlBzS3pTc3hwcHhQT1YxekFBVXlVbGc9PSIsInZhbHVlIjoiditlZ1NNa3VvaXNRcTJRWTk1RHNZTG9QODN4bWtBTDRhbnF6WTdZS1phb0pyUnlhNEdHQ2d4YmNvTGRKUXJMZ2Y5NEljLzlCTndQYmRUUEdCVWgvbmZVL3d2M1V0UVNxbmQ5VWdoM1NldXF3YS93KzlEZndLWjV3ZGRXV2w4dzkiLCJtYWMiOiIyZThjMmIwMTdjZmYxODAxNWJhOTNiNTIzM2JiYjY0YTM2M2FiZTAxMmEyZWQzNDE2N2E3NmYzY2RmYTAwYjUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WR19pjDDCaSy1nFQfLF0llRCMr5qDSh1HZpER2CZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xpcSY39d1Zg3R3trr7BnkKsq5KESCQSEoPf4iQQv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1387312116 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Aug 2025 21:23:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387312116\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2047006936 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WR19pjDDCaSy1nFQfLF0llRCMr5qDSh1HZpER2CZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>4</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>6</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>-</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">https://martfury.gc/checkout/3562afa824d90efaece25a60fd1d50a8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756416185\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756416185</span></span> {<a class=sf-dump-ref href=#sf-dump-2047006936-ref24651 title=\"3 occurrences\">#4651</a><samp data-depth=3 id=sf-dump-2047006936-ref24651 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000122b0000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 28, 2025\n- 00:00:06.962104 from now\nDST Off\">2025-08-28 21:23:05.572577 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4652</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>db532641fa6fbd0ec8cd3cf8d0aef707</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4653</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">db532641fa6fbd0ec8cd3cf8d0aef707</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>39</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>1</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"15 characters\">Audio Equipment</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>592.0</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4654</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"14 characters\">products/7.jpg</span>\"\n              \"<span class=sf-dump-key>attributes</span>\" => \"<span class=sf-dump-str title=\"25 characters\">(Color: Black, Size: XXL)</span>\"\n              \"<span class=sf-dump-key>taxRate</span>\" => <span class=sf-dump-num>10.0</span>\n              \"<span class=sf-dump-key>taxClasses</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>VAT</span>\" => <span class=sf-dump-num>10.0</span>\n              </samp>]\n              \"<span class=sf-dump-key>options</span>\" => []\n              \"<span class=sf-dump-key>extras</span>\" => []\n              \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SW-149-A0</span>\"\n              \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-num>690.0</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>10.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756416185\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756416185</span></span> {<a class=sf-dump-ref>#4655</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000122f0000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 28, 2025\n- 00:00:06.962584 from now\nDST Off\">2025-08-28 21:23:05.572545 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756416185\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756416185</span></span> {<a class=sf-dump-ref>#4656</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012300000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 28, 2025\n- 00:00:06.962641 from now\nDST Off\">2025-08-28 21:23:05.572539 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">cod</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>viewed_product</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-num>1756415873</span>\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">3562afa824d90efaece25a60fd1d50a8</span>\"\n  \"<span class=sf-dump-key>23a9b5956568711fb2583ebe79f952bf</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>address_id</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n    \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:28</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>address_id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n        \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n        \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n        \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n        \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n        \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756416185\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756416185</span></span> {<a class=sf-dump-ref href=#sf-dump-2047006936-ref24651 title=\"3 occurrences\">#4651</a>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>76</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address_id</span>\" => <span class=sf-dump-num>69</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756416185\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756416185</span></span> {<a class=sf-dump-ref href=#sf-dump-2047006936-ref24651 title=\"3 occurrences\">#4651</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">branch_pickup</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>branch_pickup</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>pickup_branch</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Branch Pickup</span>\"\n              \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Please select your preferred pickup location</span>\"\n              \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>0.0</span>\n              \"<span class=sf-dump-key>disabled</span>\" => <span class=sf-dump-const>false</span>\n              \"<span class=sf-dump-key>option</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">branch_pickup</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>is_new_address</span>\" => <span class=sf-dump-const>false</span>\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047006936\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://martfury.gc/ajax/checkout/update", "action_name": "public.ajax.checkout.update", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicUpdateCheckoutController@__invoke"}, "badge": null}}