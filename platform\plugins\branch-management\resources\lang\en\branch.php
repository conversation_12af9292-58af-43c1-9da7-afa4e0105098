<?php

return [
    'name' => 'Branches',
    'create' => 'New branch',
    'edit' => 'Edit branch ":name"',
    'list' => 'List branches',
    'menu_name' => 'Branches',
    'none' => 'None',
    'model' => 'Branch',
    'models' => 'Branches',
    'select_branch' => 'Select a branch...',
    'branch_information' => 'Branch Information',
    'contact_information' => 'Contact Information',
    'manager_information' => 'Manager Information',
    'location_information' => 'Location Information',
    'pickup_information' => 'Pickup Information',
    'operating_hours' => 'Operating Hours',
    'facilities' => 'Facilities',
    'special_instructions' => 'Special Instructions',

    'fields' => [
        'name' => 'Name',
        'description' => 'Description',
        'address' => 'Address',
        'phone' => 'Phone',
        'email' => 'Email',
        'manager_name' => 'Manager Name',
        'manager_phone' => 'Manager Phone',
        'manager_email' => 'Manager Email',
        'city' => 'City',
        'state' => 'State',
        'country' => 'Country',
        'zip_code' => 'Zip Code',
        'latitude' => 'Latitude',
        'longitude' => 'Longitude',
        'pickup_fee' => 'Pickup Fee',
        'is_featured' => 'Is Featured',
        'is_pickup_available' => 'Pickup Available',
        'operating_hours' => 'Operating Hours',
        'facilities' => 'Facilities',
        'special_instructions' => 'Special Instructions',
        'image' => 'Branch Image',
        'gallery' => 'Gallery',
    ],

    'statuses' => [
        'published' => 'Published',
        'draft' => 'Draft',
        'pending' => 'Pending',
    ],

    'notices' => [
        'no_select' => 'Please select at least one record to take this action!',
        'update_success_message' => 'Updated successfully',
        'delete_success_message' => 'Deleted successfully',
        'cannot_delete' => 'Branch could not be deleted',
    ],

    'pickup' => [
        'title' => 'Branch Pickup',
        'description' => 'Pick up your order from one of our branches',
        'select_branch' => 'Select pickup branch',
        'branch_details' => 'Branch Details',
        'pickup_fee' => 'Pickup Fee',
        'operating_hours' => 'Operating Hours',
        'contact_info' => 'Contact Information',
        'special_instructions' => 'Special Instructions',
        'no_branches_available' => 'No pickup branches available in selected city',
        'select_city_first' => 'Please select a city to view available pickup branches',
        'loading_branches' => 'Loading branches...',
        'select_city' => 'Select a city...',
        'select_branch' => 'Select a branch...',
        'email_note' => 'Please visit :branch_name to collect your order. Bring your order confirmation for verification.',
        'checkout_description' => 'Please select your preferred pickup location',
        'select_city_label' => 'Select City',
        'select_branch_label' => 'Select Branch',
        'city_help' => 'Choose the city where you want to pickup your order',
        'branch_help' => 'Choose the specific branch location for pickup',

        // Admin Order Page
        'order_notice' => 'This order is set for branch pickup.',
        'information' => 'Information',

        // Email Templates
        'email_notice' => 'This order is scheduled for branch pickup.',
        'email_instructions' => 'Important Pickup Instructions:',
        'instruction_1' => 'Please bring a valid photo ID when picking up your order.',
        'instruction_2' => 'Have your order number ready for verification.',
        'instruction_3' => 'Check branch operating hours before visiting.',

        // Shipment Pages
        'shipment_title' => 'Branch Pickup Delivery',
        'shipment_notice' => 'This shipment is for branch pickup - no home delivery required.',
        'delivery_type' => 'Delivery Type',
        'destination_branch' => 'Destination Branch',
        'shipment_instructions' => 'Delivery Instructions for Branch:',
        'shipment_instruction_1' => 'Deliver items to the specified branch location.',
        'shipment_instruction_2' => 'Notify branch manager upon delivery completion.',
        'shipment_instruction_3' => 'Customer will be contacted for pickup notification.',
    ],

    'status' => [
        'open' => 'Open',
        'closed' => 'Closed',
    ],

    'manager_information' => 'Manager Information',
    'special_instructions' => 'Special Instructions',
    'information' => 'Information',
    'name' => 'Branch Name',
    'address' => 'Address',
    'phone' => 'Phone',
    'email' => 'Email',
    'pickup_fee' => 'Pickup Fee',
    'manager_name' => 'Manager Name',
    'contact_phone' => 'Contact Phone',
    'branch_manager' => 'Branch Manager',
    'delivery_instructions' => 'Delivery Instructions',
    'today' => 'Today',
    'open_now' => 'Open Now',
    'closed_now' => 'Closed Now',
    'closed' => 'Closed',
    'view_all_hours' => 'View all operating hours',

    'days' => [
        'monday' => 'Monday',
        'tuesday' => 'Tuesday',
        'wednesday' => 'Wednesday',
        'thursday' => 'Thursday',
        'friday' => 'Friday',
        'saturday' => 'Saturday',
        'sunday' => 'Sunday',
    ],

    'settings' => [
        'enable_pickup' => 'Enable Branch Pickup',
        'default_pickup_fee' => 'Default Pickup Fee',
        'default_pickup_fee_help' => 'Default fee for branch pickup (can be overridden per branch)',
        'show_on_product_page' => 'Show on Product Page',
        'show_on_product_page_help' => 'Display branch availability on product pages',
    ],

    'email' => [
        'title' => 'Branch Management Email Settings',
        'description' => 'Email template variables for branch pickup orders',
        'variables' => [
            'branch_pickup_info' => 'Complete branch pickup information block',
            'branch_name' => 'Name of the pickup branch',
            'branch_address' => 'Full address of the pickup branch',
            'branch_phone' => 'Phone number of the pickup branch',
            'branch_email' => 'Email address of the pickup branch',
            'pickup_fee' => 'Formatted pickup fee amount',
        ],
    ],
];
