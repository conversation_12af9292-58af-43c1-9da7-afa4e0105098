{"__meta": {"id": "01K3S71QY84T5F0PBNKCCX559A", "datetime": "2025-08-28 20:49:00", "utime": **********.362975, "method": "GET", "uri": "/admin/email-logs", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 360, "start": 1756414137.369274, "end": **********.363035, "duration": 2.9937610626220703, "duration_str": "2.99s", "measures": [{"label": "Booting", "start": 1756414137.369274, "relative_start": 0, "end": **********.029289, "relative_end": **********.029289, "duration": 1.****************, "duration_str": "1.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.029304, "relative_start": 1.****************, "end": **********.363043, "relative_end": 8.106231689453125e-06, "duration": 1.****************, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.054271, "relative_start": 1.****************, "end": **********.072343, "relative_end": **********.072343, "duration": 0.018072128295898438, "duration_str": "18.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::table-info", "start": **********.165917, "relative_start": 1.****************, "end": **********.165917, "relative_end": **********.165917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c33cfbd01dd1d76718fcd68287a40728", "start": **********.170949, "relative_start": 1.****************, "end": **********.170949, "relative_end": **********.170949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::badge", "start": **********.176383, "relative_start": 1.8071091175079346, "end": **********.176383, "relative_end": **********.176383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.181337, "relative_start": 1.812063217163086, "end": **********.353297, "relative_end": **********.353297, "duration": 1.1719598770141602, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::table", "start": **********.182306, "relative_start": 1.8130321502685547, "end": **********.182306, "relative_end": **********.182306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::base-table", "start": **********.18282, "relative_start": 1.8135461807250977, "end": **********.18282, "relative_end": **********.18282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::bulk-action", "start": **********.187571, "relative_start": 1.8182971477508545, "end": **********.187571, "relative_end": **********.187571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.191023, "relative_start": 1.821749210357666, "end": **********.191023, "relative_end": **********.191023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.192235, "relative_start": 1.8229610919952393, "end": **********.192235, "relative_end": **********.192235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.193421, "relative_start": 1.8241469860076904, "end": **********.193421, "relative_end": **********.193421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6f3b10173cc6f5c541f27080145e1a40", "start": **********.196089, "relative_start": 1.826815128326416, "end": **********.196089, "relative_end": **********.196089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::42e1966f95bce065f65d4b22e53f3772", "start": **********.197436, "relative_start": 1.8281621932983398, "end": **********.197436, "relative_end": **********.197436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.199183, "relative_start": 1.829909086227417, "end": **********.199183, "relative_end": **********.199183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0c8728926b3975e33a051ebb6ef68e5d", "start": **********.200498, "relative_start": 1.8312242031097412, "end": **********.200498, "relative_end": **********.200498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.20118, "relative_start": 1.8319060802459717, "end": **********.20118, "relative_end": **********.20118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.202404, "relative_start": 1.833130121231079, "end": **********.202404, "relative_end": **********.202404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::modal", "start": **********.203366, "relative_start": 1.834092140197754, "end": **********.203366, "relative_end": **********.203366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.205024, "relative_start": 1.8357501029968262, "end": **********.205024, "relative_end": **********.205024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.207301, "relative_start": 1.838027000427246, "end": **********.207301, "relative_end": **********.207301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.210292, "relative_start": 1.8410181999206543, "end": **********.210292, "relative_end": **********.210292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.212497, "relative_start": 1.8432230949401855, "end": **********.212497, "relative_end": **********.212497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.213552, "relative_start": 1.84427809715271, "end": **********.213552, "relative_end": **********.213552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.21552, "relative_start": 1.8462460041046143, "end": **********.21552, "relative_end": **********.21552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.216721, "relative_start": 1.847447156906128, "end": **********.216721, "relative_end": **********.216721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.217827, "relative_start": 1.84855318069458, "end": **********.217827, "relative_end": **********.217827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.218311, "relative_start": 1.8490371704101562, "end": **********.218311, "relative_end": **********.218311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.218663, "relative_start": 1.8493890762329102, "end": **********.218663, "relative_end": **********.218663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.220107, "relative_start": 1.8508331775665283, "end": **********.220107, "relative_end": **********.220107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.220994, "relative_start": 1.8517200946807861, "end": **********.220994, "relative_end": **********.220994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.221843, "relative_start": 1.8525691032409668, "end": **********.221843, "relative_end": **********.221843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.223156, "relative_start": 1.853882074356079, "end": **********.223156, "relative_end": **********.223156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.223705, "relative_start": 1.85443115234375, "end": **********.223705, "relative_end": **********.223705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.225488, "relative_start": 1.8562140464782715, "end": **********.225488, "relative_end": **********.225488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.226981, "relative_start": 1.8577070236206055, "end": **********.226981, "relative_end": **********.226981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.227609, "relative_start": 1.858335018157959, "end": **********.227609, "relative_end": **********.227609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.227996, "relative_start": 1.85872220993042, "end": **********.227996, "relative_end": **********.227996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.229564, "relative_start": 1.8602900505065918, "end": **********.229564, "relative_end": **********.229564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.23067, "relative_start": 1.861396074295044, "end": **********.23067, "relative_end": **********.23067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.231734, "relative_start": 1.8624601364135742, "end": **********.231734, "relative_end": **********.231734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.232224, "relative_start": 1.862950086593628, "end": **********.232224, "relative_end": **********.232224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.232565, "relative_start": 1.8632910251617432, "end": **********.232565, "relative_end": **********.232565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::script", "start": **********.236787, "relative_start": 1.8675131797790527, "end": **********.236787, "relative_end": **********.236787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.master", "start": **********.240025, "relative_start": 1.870751142501831, "end": **********.240025, "relative_end": **********.240025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.before-content", "start": **********.245845, "relative_start": 1.8765711784362793, "end": **********.245845, "relative_end": **********.245845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.header", "start": **********.246542, "relative_start": 1.8772680759429932, "end": **********.246542, "relative_end": **********.246542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3a4eb377d01a3c4bb09865b43ffbd313", "start": **********.248378, "relative_start": 1.8791041374206543, "end": **********.248378, "relative_end": **********.248378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.249302, "relative_start": 1.8800280094146729, "end": **********.249302, "relative_end": **********.249302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.navbar-input", "start": **********.264908, "relative_start": 1.8956341743469238, "end": **********.264908, "relative_end": **********.264908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.265682, "relative_start": 1.8964080810546875, "end": **********.265682, "relative_end": **********.265682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.267796, "relative_start": 1.898522138595581, "end": **********.267796, "relative_end": **********.267796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.268451, "relative_start": 1.899177074432373, "end": **********.268451, "relative_end": **********.268451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.268946, "relative_start": 1.899672031402588, "end": **********.268946, "relative_end": **********.268946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.269915, "relative_start": 1.9006412029266357, "end": **********.269915, "relative_end": **********.269915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d2cfde89f704c31422aff2fae16ddb81", "start": **********.271268, "relative_start": 1.901993989944458, "end": **********.271268, "relative_end": **********.271268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.theme-toggle", "start": **********.272395, "relative_start": 1.903120994567871, "end": **********.272395, "relative_end": **********.272395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a5645d2a1f3c74251fc89224c575fed8", "start": **********.275684, "relative_start": 1.9064102172851562, "end": **********.275684, "relative_end": **********.275684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.nav-item", "start": **********.27888, "relative_start": 1.9096059799194336, "end": **********.27888, "relative_end": **********.27888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::639d159f54869d7a8362974885dec505", "start": **********.280262, "relative_start": 1.9109880924224854, "end": **********.280262, "relative_end": **********.280262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/contact::partials.notification", "start": **********.283811, "relative_start": 1.9145371913909912, "end": **********.283811, "relative_end": **********.283811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.285354, "relative_start": 1.9160799980163574, "end": **********.285354, "relative_end": **********.285354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.title", "start": **********.286639, "relative_start": 1.9173650741577148, "end": **********.286639, "relative_end": **********.286639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.actions", "start": **********.287481, "relative_start": 1.9182071685791016, "end": **********.287481, "relative_end": **********.287481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.287971, "relative_start": 1.9186971187591553, "end": **********.287971, "relative_end": **********.287971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.309689, "relative_start": 1.9404151439666748, "end": **********.309689, "relative_end": **********.309689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.notification", "start": **********.329516, "relative_start": 1.9602420330047607, "end": **********.329516, "relative_end": **********.329516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8394ebb1c2841e3a1166cd3fb0a6e03f", "start": **********.331294, "relative_start": 1.9620201587677002, "end": **********.331294, "relative_end": **********.331294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.333146, "relative_start": 1.9638721942901611, "end": **********.333146, "relative_end": **********.333146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.347903, "relative_start": 1.9786291122436523, "end": **********.347903, "relative_end": **********.347903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.user-menu", "start": **********.34894, "relative_start": 1.979665994644165, "end": **********.34894, "relative_end": **********.34894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.353203, "relative_start": 1.98392915725708, "end": **********.353203, "relative_end": **********.353203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d059faaba602d6895d68258ab3c890a6", "start": **********.35503, "relative_start": 1.9857561588287354, "end": **********.35503, "relative_end": **********.35503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.356821, "relative_start": 1.9875471591949463, "end": **********.356821, "relative_end": **********.356821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3cb4601eb64a80dc01a3c268590a3c8", "start": **********.358904, "relative_start": 1.9896299839019775, "end": **********.358904, "relative_end": **********.358904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.359777, "relative_start": 1.9905030727386475, "end": **********.359777, "relative_end": **********.359777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.aside", "start": **********.36078, "relative_start": 1.9915060997009277, "end": **********.36078, "relative_end": **********.36078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3a4eb377d01a3c4bb09865b43ffbd313", "start": **********.361898, "relative_start": 1.992624044418335, "end": **********.361898, "relative_end": **********.361898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.362183, "relative_start": 1.9929091930389404, "end": **********.362183, "relative_end": **********.362183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.364139, "relative_start": 1.9948651790618896, "end": **********.364139, "relative_end": **********.364139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d059faaba602d6895d68258ab3c890a6", "start": **********.364895, "relative_start": 1.9956212043762207, "end": **********.364895, "relative_end": **********.364895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.365286, "relative_start": 1.9960122108459473, "end": **********.365286, "relative_end": **********.365286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3cb4601eb64a80dc01a3c268590a3c8", "start": **********.365904, "relative_start": 1.9966301918029785, "end": **********.365904, "relative_end": **********.365904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.366151, "relative_start": 1.9968771934509277, "end": **********.366151, "relative_end": **********.366151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.sidebar", "start": **********.36679, "relative_start": 1.99751615524292, "end": **********.36679, "relative_end": **********.36679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav", "start": **********.367225, "relative_start": 1.9979510307312012, "end": **********.367225, "relative_end": **********.367225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.393172, "relative_start": 2.023898124694824, "end": **********.393172, "relative_end": **********.393172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.394786, "relative_start": 2.0255119800567627, "end": **********.394786, "relative_end": **********.394786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e3b17f7ce9738894b58a8b70b9624457", "start": **********.397189, "relative_start": 2.0279150009155273, "end": **********.397189, "relative_end": **********.397189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.398466, "relative_start": 2.0291922092437744, "end": **********.398466, "relative_end": **********.398466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.399358, "relative_start": 2.0300841331481934, "end": **********.399358, "relative_end": **********.399358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0c6e6838aa476b78aace81114936689c", "start": **********.402002, "relative_start": 2.0327281951904297, "end": **********.402002, "relative_end": **********.402002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.403295, "relative_start": 2.0340211391448975, "end": **********.403295, "relative_end": **********.403295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.404029, "relative_start": 2.034754991531372, "end": **********.404029, "relative_end": **********.404029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.405243, "relative_start": 2.0359690189361572, "end": **********.405243, "relative_end": **********.405243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::37fae22c8e215ea2e54e69a5e3a007cc", "start": **********.410362, "relative_start": 2.041088104248047, "end": **********.410362, "relative_end": **********.410362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.41271, "relative_start": 2.043436050415039, "end": **********.41271, "relative_end": **********.41271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8916176d99d4ae2024cd36e11e35b821", "start": **********.415382, "relative_start": 2.0461080074310303, "end": **********.415382, "relative_end": **********.415382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.416849, "relative_start": 2.047574996948242, "end": **********.416849, "relative_end": **********.416849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.417266, "relative_start": 2.047991991043091, "end": **********.417266, "relative_end": **********.417266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.417826, "relative_start": 2.0485520362854004, "end": **********.417826, "relative_end": **********.417826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd27433a6607127acdaf6dc541ab2435", "start": **********.419482, "relative_start": 2.05020809173584, "end": **********.419482, "relative_end": **********.419482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.420728, "relative_start": 2.0514540672302246, "end": **********.420728, "relative_end": **********.420728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "start": **********.424741, "relative_start": 2.055467128753662, "end": **********.424741, "relative_end": **********.424741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.426721, "relative_start": 2.0574471950531006, "end": **********.426721, "relative_end": **********.426721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.427423, "relative_start": 2.0581490993499756, "end": **********.427423, "relative_end": **********.427423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.428298, "relative_start": 2.0590240955352783, "end": **********.428298, "relative_end": **********.428298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::080b92e00b37bcc97c1cd249894494a2", "start": **********.431245, "relative_start": 2.0619711875915527, "end": **********.431245, "relative_end": **********.431245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.432488, "relative_start": 2.063214063644409, "end": **********.432488, "relative_end": **********.432488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6a26943e77184871de1629f41c534094", "start": **********.434852, "relative_start": 2.065577983856201, "end": **********.434852, "relative_end": **********.434852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.435879, "relative_start": 2.0666050910949707, "end": **********.435879, "relative_end": **********.435879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8f29f8012139c7a3eb6593c906e1db38", "start": **********.437554, "relative_start": 2.068279981613159, "end": **********.437554, "relative_end": **********.437554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.439634, "relative_start": 2.0703601837158203, "end": **********.439634, "relative_end": **********.439634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.440494, "relative_start": 2.0712201595306396, "end": **********.440494, "relative_end": **********.440494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.44214, "relative_start": 2.072866201400757, "end": **********.44214, "relative_end": **********.44214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4553f5b37130b2effba490dbdf5419d2", "start": **********.446751, "relative_start": 2.077477216720581, "end": **********.446751, "relative_end": **********.446751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.448028, "relative_start": 2.078754186630249, "end": **********.448028, "relative_end": **********.448028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::59c947fc9b2121a5885d4f4e7b1242d8", "start": **********.450426, "relative_start": 2.0811522006988525, "end": **********.450426, "relative_end": **********.450426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.451617, "relative_start": 2.082343101501465, "end": **********.451617, "relative_end": **********.451617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1e9698c460b468bfcaf0f7dbbebf9bf0", "start": **********.453421, "relative_start": 2.0841472148895264, "end": **********.453421, "relative_end": **********.453421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.454878, "relative_start": 2.085604190826416, "end": **********.454878, "relative_end": **********.454878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d4eb4544a5328bea40b7b01743b8f82", "start": **********.458588, "relative_start": 2.0893139839172363, "end": **********.458588, "relative_end": **********.458588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.46004, "relative_start": 2.090766191482544, "end": **********.46004, "relative_end": **********.46004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5270fef4db64e6c2fedf42ea8ac88f25", "start": **********.462679, "relative_start": 2.09340500831604, "end": **********.462679, "relative_end": **********.462679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.464073, "relative_start": 2.094799041748047, "end": **********.464073, "relative_end": **********.464073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::84f17fac377525e2e49f32058361220b", "start": **********.466148, "relative_start": 2.0968739986419678, "end": **********.466148, "relative_end": **********.466148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.467238, "relative_start": 2.09796404838562, "end": **********.467238, "relative_end": **********.467238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5270fef4db64e6c2fedf42ea8ac88f25", "start": **********.468636, "relative_start": 2.0993621349334717, "end": **********.468636, "relative_end": **********.468636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.469341, "relative_start": 2.100067138671875, "end": **********.469341, "relative_end": **********.469341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c8617dee734f51544a3883923ddca6f", "start": **********.471569, "relative_start": 2.102295160293579, "end": **********.471569, "relative_end": **********.471569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.473661, "relative_start": 2.104387044906616, "end": **********.473661, "relative_end": **********.473661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3d3bfe5e8598abeb74083f6c26233cb5", "start": **********.478901, "relative_start": 2.1096270084381104, "end": **********.478901, "relative_end": **********.478901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.481075, "relative_start": 2.1118011474609375, "end": **********.481075, "relative_end": **********.481075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::35fe997a7b87ef55d749630606a50a1b", "start": **********.484724, "relative_start": 2.115450143814087, "end": **********.484724, "relative_end": **********.484724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.490598, "relative_start": 2.121324062347412, "end": **********.490598, "relative_end": **********.490598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a4f1583597dec7e67a8ae044f0915dbe", "start": **********.493684, "relative_start": 2.1244101524353027, "end": **********.493684, "relative_end": **********.493684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.494862, "relative_start": 2.1255881786346436, "end": **********.494862, "relative_end": **********.494862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c0cbd16b0cc2226ec5536610974ba3c3", "start": **********.497611, "relative_start": 2.1283371448516846, "end": **********.497611, "relative_end": **********.497611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.498648, "relative_start": 2.1293740272521973, "end": **********.498648, "relative_end": **********.498648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::70b8df706e60982a72f15e9e2d486203", "start": **********.500695, "relative_start": 2.1314210891723633, "end": **********.500695, "relative_end": **********.500695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.501663, "relative_start": 2.1323890686035156, "end": **********.501663, "relative_end": **********.501663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.502468, "relative_start": 2.1331942081451416, "end": **********.502468, "relative_end": **********.502468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dc78b90963e9d9963376e0e829411cea", "start": **********.504977, "relative_start": 2.1357030868530273, "end": **********.504977, "relative_end": **********.504977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.507214, "relative_start": 2.1379401683807373, "end": **********.507214, "relative_end": **********.507214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.510775, "relative_start": 2.1415011882781982, "end": **********.510775, "relative_end": **********.510775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.512465, "relative_start": 2.14319109916687, "end": **********.512465, "relative_end": **********.512465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::481a833ebeb573258c941c925aa45f7b", "start": **********.514486, "relative_start": 2.145212173461914, "end": **********.514486, "relative_end": **********.514486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.515746, "relative_start": 2.146472215652466, "end": **********.515746, "relative_end": **********.515746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b42bb0aa5fceca31ad61711414a614f0", "start": **********.517429, "relative_start": 2.1481552124023438, "end": **********.517429, "relative_end": **********.517429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.518425, "relative_start": 2.149151086807251, "end": **********.518425, "relative_end": **********.518425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.519225, "relative_start": 2.1499509811401367, "end": **********.519225, "relative_end": **********.519225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.524603, "relative_start": 2.1553289890289307, "end": **********.524603, "relative_end": **********.524603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.526944, "relative_start": 2.157670021057129, "end": **********.526944, "relative_end": **********.526944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.527874, "relative_start": 2.158600091934204, "end": **********.527874, "relative_end": **********.527874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.528785, "relative_start": 2.159511089324951, "end": **********.528785, "relative_end": **********.528785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::471e83668278198d730a7a3f4a475d45", "start": **********.530983, "relative_start": 2.1617090702056885, "end": **********.530983, "relative_end": **********.530983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.532074, "relative_start": 2.1628000736236572, "end": **********.532074, "relative_end": **********.532074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.533029, "relative_start": 2.163755178451538, "end": **********.533029, "relative_end": **********.533029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.533695, "relative_start": 2.1644210815429688, "end": **********.533695, "relative_end": **********.533695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7b40194b2b4ba91a975fc9aafe2d3e8", "start": **********.535727, "relative_start": 2.1664531230926514, "end": **********.535727, "relative_end": **********.535727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.536776, "relative_start": 2.1675021648406982, "end": **********.536776, "relative_end": **********.536776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.537164, "relative_start": 2.1678900718688965, "end": **********.537164, "relative_end": **********.537164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.537854, "relative_start": 2.1685800552368164, "end": **********.537854, "relative_end": **********.537854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::70b8df706e60982a72f15e9e2d486203", "start": **********.539401, "relative_start": 2.1701271533966064, "end": **********.539401, "relative_end": **********.539401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.540582, "relative_start": 2.1713080406188965, "end": **********.540582, "relative_end": **********.540582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::acb69140835a74210411469faeab3034", "start": **********.545864, "relative_start": 2.1765902042388916, "end": **********.545864, "relative_end": **********.545864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.54767, "relative_start": 2.178395986557007, "end": **********.54767, "relative_end": **********.54767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.54815, "relative_start": 2.1788761615753174, "end": **********.54815, "relative_end": **********.54815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.548824, "relative_start": 2.1795501708984375, "end": **********.548824, "relative_end": **********.548824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::19cd49cd69455e40dc223df6b4eaf954", "start": **********.550952, "relative_start": 2.181678056716919, "end": **********.550952, "relative_end": **********.550952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.551941, "relative_start": 2.1826670169830322, "end": **********.551941, "relative_end": **********.551941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.552738, "relative_start": 2.1834640502929688, "end": **********.552738, "relative_end": **********.552738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2979b72aeeca0047ecdecc3ad66e7e16", "start": **********.554819, "relative_start": 2.1855452060699463, "end": **********.554819, "relative_end": **********.554819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.557152, "relative_start": 2.187878131866455, "end": **********.557152, "relative_end": **********.557152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.559683, "relative_start": 2.1904091835021973, "end": **********.559683, "relative_end": **********.559683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3c40febd70fcdc245d99ae7cd02cface", "start": **********.563191, "relative_start": 2.1939170360565186, "end": **********.563191, "relative_end": **********.563191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.565484, "relative_start": 2.1962101459503174, "end": **********.565484, "relative_end": **********.565484, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::90ccac5c8bbb25741ef262bfd81c7551", "start": **********.569236, "relative_start": 2.1999621391296387, "end": **********.569236, "relative_end": **********.569236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.571279, "relative_start": 2.20200514793396, "end": **********.571279, "relative_end": **********.571279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.574333, "relative_start": 2.205059051513672, "end": **********.574333, "relative_end": **********.574333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.576857, "relative_start": 2.20758318901062, "end": **********.576857, "relative_end": **********.576857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d4eb4544a5328bea40b7b01743b8f82", "start": **********.582179, "relative_start": 2.212905168533325, "end": **********.582179, "relative_end": **********.582179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.584497, "relative_start": 2.2152230739593506, "end": **********.584497, "relative_end": **********.584497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.58614, "relative_start": 2.2168660163879395, "end": **********.58614, "relative_end": **********.58614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7ced212b797c29086a7922a858f3070", "start": **********.591054, "relative_start": 2.2217800617218018, "end": **********.591054, "relative_end": **********.591054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.594916, "relative_start": 2.225642204284668, "end": **********.594916, "relative_end": **********.594916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.596191, "relative_start": 2.226917028427124, "end": **********.596191, "relative_end": **********.596191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.597753, "relative_start": 2.2284791469573975, "end": **********.597753, "relative_end": **********.597753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d41a7757b46012fb4a0d6634d04a1e0", "start": **********.60065, "relative_start": 2.2313761711120605, "end": **********.60065, "relative_end": **********.60065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.6021, "relative_start": 2.232825994491577, "end": **********.6021, "relative_end": **********.6021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.602459, "relative_start": 2.233185052871704, "end": **********.602459, "relative_end": **********.602459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.603249, "relative_start": 2.2339751720428467, "end": **********.603249, "relative_end": **********.603249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::90ccac5c8bbb25741ef262bfd81c7551", "start": **********.605188, "relative_start": 2.2359139919281006, "end": **********.605188, "relative_end": **********.605188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.607985, "relative_start": 2.23871111869812, "end": **********.607985, "relative_end": **********.607985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.614423, "relative_start": 2.2451491355895996, "end": **********.614423, "relative_end": **********.614423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.616773, "relative_start": 2.2474989891052246, "end": **********.616773, "relative_end": **********.616773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.618833, "relative_start": 2.249559164047241, "end": **********.618833, "relative_end": **********.618833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.622875, "relative_start": 2.25360107421875, "end": **********.622875, "relative_end": **********.622875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.624669, "relative_start": 2.2553951740264893, "end": **********.624669, "relative_end": **********.624669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.626836, "relative_start": 2.2575621604919434, "end": **********.626836, "relative_end": **********.626836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8c52d9b1ef0685ec10fdc3e877751e02", "start": **********.629398, "relative_start": 2.2601242065429688, "end": **********.629398, "relative_end": **********.629398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.632331, "relative_start": 2.263056993484497, "end": **********.632331, "relative_end": **********.632331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.634462, "relative_start": 2.265188217163086, "end": **********.634462, "relative_end": **********.634462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::42668be6e8e5266862c6994eaa88bb55", "start": **********.63894, "relative_start": 2.2696661949157715, "end": **********.63894, "relative_end": **********.63894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.640379, "relative_start": 2.2711050510406494, "end": **********.640379, "relative_end": **********.640379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.64116, "relative_start": 2.271886110305786, "end": **********.64116, "relative_end": **********.64116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.642376, "relative_start": 2.273102045059204, "end": **********.642376, "relative_end": **********.642376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.644326, "relative_start": 2.275052070617676, "end": **********.644326, "relative_end": **********.644326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::311d8c591d63d3cbd12dceb2fb1ac1c1", "start": **********.647461, "relative_start": 2.2781870365142822, "end": **********.647461, "relative_end": **********.647461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.649137, "relative_start": 2.279863119125366, "end": **********.649137, "relative_end": **********.649137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d41a7757b46012fb4a0d6634d04a1e0", "start": **********.650644, "relative_start": 2.281370162963867, "end": **********.650644, "relative_end": **********.650644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.652027, "relative_start": 2.2827529907226562, "end": **********.652027, "relative_end": **********.652027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.65398, "relative_start": 2.2847061157226562, "end": **********.65398, "relative_end": **********.65398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.655653, "relative_start": 2.286379098892212, "end": **********.655653, "relative_end": **********.655653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.657727, "relative_start": 2.2884531021118164, "end": **********.657727, "relative_end": **********.657727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.660401, "relative_start": 2.2911272048950195, "end": **********.660401, "relative_end": **********.660401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.662161, "relative_start": 2.2928872108459473, "end": **********.662161, "relative_end": **********.662161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.662854, "relative_start": 2.2935800552368164, "end": **********.662854, "relative_end": **********.662854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.663876, "relative_start": 2.294602155685425, "end": **********.663876, "relative_end": **********.663876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::613233f0072612a02c74dd1699c0b74c", "start": **********.667287, "relative_start": 2.298013210296631, "end": **********.667287, "relative_end": **********.667287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.669166, "relative_start": 2.2998921871185303, "end": **********.669166, "relative_end": **********.669166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::16225ede2ef5cc17292fd2eb9026fc80", "start": **********.671629, "relative_start": 2.3023550510406494, "end": **********.671629, "relative_end": **********.671629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.674339, "relative_start": 2.305065155029297, "end": **********.674339, "relative_end": **********.674339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.675758, "relative_start": 2.3064839839935303, "end": **********.675758, "relative_end": **********.675758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::46010cb1cb88bb5ead5d94603a4a3d16", "start": **********.681714, "relative_start": 2.3124401569366455, "end": **********.681714, "relative_end": **********.681714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.684205, "relative_start": 2.3149311542510986, "end": **********.684205, "relative_end": **********.684205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.685819, "relative_start": 2.316545009613037, "end": **********.685819, "relative_end": **********.685819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::34e3d89351b1208b7f313125eec52879", "start": **********.689754, "relative_start": 2.3204801082611084, "end": **********.689754, "relative_end": **********.689754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.692066, "relative_start": 2.3227920532226562, "end": **********.692066, "relative_end": **********.692066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::67034900569133b2c83b32da3dd4f5e5", "start": **********.696516, "relative_start": 2.327242136001587, "end": **********.696516, "relative_end": **********.696516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.698538, "relative_start": 2.3292641639709473, "end": **********.698538, "relative_end": **********.698538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.700674, "relative_start": 2.331400156021118, "end": **********.700674, "relative_end": **********.700674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.701782, "relative_start": 2.332508087158203, "end": **********.701782, "relative_end": **********.701782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.703083, "relative_start": 2.3338091373443604, "end": **********.703083, "relative_end": **********.703083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.705657, "relative_start": 2.336383104324341, "end": **********.705657, "relative_end": **********.705657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.707519, "relative_start": 2.338245153427124, "end": **********.707519, "relative_end": **********.707519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.709454, "relative_start": 2.3401801586151123, "end": **********.709454, "relative_end": **********.709454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c33cfbd01dd1d76718fcd68287a40728", "start": **********.714659, "relative_start": 2.3453850746154785, "end": **********.714659, "relative_end": **********.714659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.716152, "relative_start": 2.3468780517578125, "end": **********.716152, "relative_end": **********.716152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d623715926c24f9fbc8a4b72c106d5d", "start": **********.71944, "relative_start": 2.350166082382202, "end": **********.71944, "relative_end": **********.71944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.721076, "relative_start": 2.351802110671997, "end": **********.721076, "relative_end": **********.721076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6c50fc55276d93f8ed03f5c85273b6cc", "start": **********.724693, "relative_start": 2.355419158935547, "end": **********.724693, "relative_end": **********.724693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.725991, "relative_start": 2.356717109680176, "end": **********.725991, "relative_end": **********.725991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::084d2b43c9ab4b881d9b34a15580aa2d", "start": **********.728474, "relative_start": 2.3592000007629395, "end": **********.728474, "relative_end": **********.728474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.729659, "relative_start": 2.3603851795196533, "end": **********.729659, "relative_end": **********.729659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b33d20952e90e5c4a596ff58ad010448", "start": **********.732221, "relative_start": 2.3629469871520996, "end": **********.732221, "relative_end": **********.732221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.733294, "relative_start": 2.3640201091766357, "end": **********.733294, "relative_end": **********.733294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7db8cad89359963c1e9aa8fcc6c89817", "start": **********.735225, "relative_start": 2.3659510612487793, "end": **********.735225, "relative_end": **********.735225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.736186, "relative_start": 2.3669121265411377, "end": **********.736186, "relative_end": **********.736186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.737113, "relative_start": 2.3678390979766846, "end": **********.737113, "relative_end": **********.737113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.740942, "relative_start": 2.3716681003570557, "end": **********.740942, "relative_end": **********.740942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.742898, "relative_start": 2.373624086380005, "end": **********.742898, "relative_end": **********.742898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.745178, "relative_start": 2.375904083251953, "end": **********.745178, "relative_end": **********.745178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::325be2a8c3ca3843efa76c03adaee1dc", "start": **********.74952, "relative_start": 2.380246162414551, "end": **********.74952, "relative_end": **********.74952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.75131, "relative_start": 2.3820362091064453, "end": **********.75131, "relative_end": **********.75131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e12a669ffa0346a27198bed32e63b7ba", "start": **********.753719, "relative_start": 2.3844451904296875, "end": **********.753719, "relative_end": **********.753719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.755649, "relative_start": 2.3863751888275146, "end": **********.755649, "relative_end": **********.755649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d0b20db301db9a47503a93a879bb206", "start": **********.758446, "relative_start": 2.389172077178955, "end": **********.758446, "relative_end": **********.758446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.759785, "relative_start": 2.3905110359191895, "end": **********.759785, "relative_end": **********.759785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b985af7bcdacbeac70eaf3979ad19f5a", "start": **********.761515, "relative_start": 2.3922410011291504, "end": **********.761515, "relative_end": **********.761515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.762502, "relative_start": 2.393228054046631, "end": **********.762502, "relative_end": **********.762502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a0bb1d43b71cff86abe626fd376492e9", "start": **********.764242, "relative_start": 2.394968032836914, "end": **********.764242, "relative_end": **********.764242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.765314, "relative_start": 2.396040201187134, "end": **********.765314, "relative_end": **********.765314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e25f2b305e6de46c04f91fa1ce50f68f", "start": **********.767051, "relative_start": 2.3977770805358887, "end": **********.767051, "relative_end": **********.767051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.768602, "relative_start": 2.3993279933929443, "end": **********.768602, "relative_end": **********.768602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69152f707ea1358f8997b77a28e38a6f", "start": **********.770178, "relative_start": 2.4009041786193848, "end": **********.770178, "relative_end": **********.770178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.772411, "relative_start": 2.40313720703125, "end": **********.772411, "relative_end": **********.772411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b85eba35d3b7929c2988678b725baebf", "start": **********.775458, "relative_start": 2.406184196472168, "end": **********.775458, "relative_end": **********.775458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.777642, "relative_start": 2.4083681106567383, "end": **********.777642, "relative_end": **********.777642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1f3d3b83c612f68036b4d79d53ae851e", "start": **********.779977, "relative_start": 2.410703182220459, "end": **********.779977, "relative_end": **********.779977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.780992, "relative_start": 2.4117181301116943, "end": **********.780992, "relative_end": **********.780992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.781819, "relative_start": 2.4125452041625977, "end": **********.781819, "relative_end": **********.781819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f143d1296cea16d82e2c87956e445593", "start": **********.78501, "relative_start": 2.415736198425293, "end": **********.78501, "relative_end": **********.78501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.786056, "relative_start": 2.4167821407318115, "end": **********.786056, "relative_end": **********.786056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8cae4e5056b67c6778a54389a62ac7a0", "start": **********.791126, "relative_start": 2.4218521118164062, "end": **********.791126, "relative_end": **********.791126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.793583, "relative_start": 2.424309015274048, "end": **********.793583, "relative_end": **********.793583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d5e509b6eb9084ec382ec05ccab41d1a", "start": **********.796281, "relative_start": 2.4270071983337402, "end": **********.796281, "relative_end": **********.796281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.797785, "relative_start": 2.428511142730713, "end": **********.797785, "relative_end": **********.797785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.798875, "relative_start": 2.4296011924743652, "end": **********.798875, "relative_end": **********.798875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e81a46563ed9378aa4d9a4fcb55e743e", "start": **********.800505, "relative_start": 2.4312310218811035, "end": **********.800505, "relative_end": **********.800505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.802357, "relative_start": 2.4330830574035645, "end": **********.802357, "relative_end": **********.802357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b33d20952e90e5c4a596ff58ad010448", "start": **********.806123, "relative_start": 2.4368491172790527, "end": **********.806123, "relative_end": **********.806123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.808735, "relative_start": 2.4394609928131104, "end": **********.808735, "relative_end": **********.808735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.810561, "relative_start": 2.441287040710449, "end": **********.810561, "relative_end": **********.810561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.812797, "relative_start": 2.4435231685638428, "end": **********.812797, "relative_end": **********.812797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.813931, "relative_start": 2.44465708732605, "end": **********.813931, "relative_end": **********.813931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.814938, "relative_start": 2.445664167404175, "end": **********.814938, "relative_end": **********.814938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da3c3de008e5793cbbdad005d78f49b1", "start": **********.8174, "relative_start": 2.4481260776519775, "end": **********.8174, "relative_end": **********.8174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.page-header", "start": **********.819237, "relative_start": 2.449963092803955, "end": **********.819237, "relative_end": **********.819237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::breadcrumb", "start": **********.820371, "relative_start": 2.451097011566162, "end": **********.820371, "relative_end": **********.820371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.footer", "start": **********.823779, "relative_start": 2.454505205154419, "end": **********.823779, "relative_end": **********.823779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.copyright", "start": **********.824843, "relative_start": 2.45556902885437, "end": **********.824843, "relative_end": **********.824843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.after-content", "start": **********.826808, "relative_start": 2.457534074783325, "end": **********.826808, "relative_end": **********.826808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::system.license-invalid", "start": **********.828006, "relative_start": 2.4587321281433105, "end": **********.828006, "relative_end": **********.828006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::alert", "start": **********.829097, "relative_start": 2.4598231315612793, "end": **********.829097, "relative_end": **********.829097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::system.partials.license-activation-modal", "start": **********.830162, "relative_start": 2.460888147354126, "end": **********.830162, "relative_end": **********.830162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::license.form", "start": **********.831873, "relative_start": 2.462599039077759, "end": **********.831873, "relative_end": **********.831873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::alert", "start": **********.833516, "relative_start": 2.4642419815063477, "end": **********.833516, "relative_end": **********.833516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::27ec08f706fece52ef1cc0ec5563cef9", "start": **********.835712, "relative_start": 2.466438055038452, "end": **********.835712, "relative_end": **********.835712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.836665, "relative_start": 2.467391014099121, "end": **********.836665, "relative_end": **********.836665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.838072, "relative_start": 2.4687981605529785, "end": **********.838072, "relative_end": **********.838072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.helper-text", "start": **********.83962, "relative_start": 2.470346212387085, "end": **********.83962, "relative_end": **********.83962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.840644, "relative_start": 2.471369981765747, "end": **********.840644, "relative_end": **********.840644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.841517, "relative_start": 2.472243070602417, "end": **********.841517, "relative_end": **********.841517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.842302, "relative_start": 2.4730281829833984, "end": **********.842302, "relative_end": **********.842302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.843908, "relative_start": 2.4746341705322266, "end": **********.843908, "relative_end": **********.843908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.helper-text", "start": **********.844864, "relative_start": 2.4755899906158447, "end": **********.844864, "relative_end": **********.844864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.84533, "relative_start": 2.4760560989379883, "end": **********.84533, "relative_end": **********.84533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.845974, "relative_start": 2.4767000675201416, "end": **********.845974, "relative_end": **********.845974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "start": **********.846774, "relative_start": 2.4775002002716064, "end": **********.846774, "relative_end": **********.846774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": **********.847577, "relative_start": 2.4783031940460205, "end": **********.847577, "relative_end": **********.847577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.848385, "relative_start": 2.4791111946105957, "end": **********.848385, "relative_end": **********.848385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.849085, "relative_start": 2.479811191558838, "end": **********.849085, "relative_end": **********.849085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.85047, "relative_start": 2.481196165084839, "end": **********.85047, "relative_end": **********.85047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.851317, "relative_start": 2.4820430278778076, "end": **********.851317, "relative_end": **********.851317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.852354, "relative_start": 2.4830801486968994, "end": **********.852354, "relative_end": **********.852354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.854067, "relative_start": 2.484793186187744, "end": **********.854067, "relative_end": **********.854067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.form", "start": **********.855687, "relative_start": 2.48641300201416, "end": **********.855687, "relative_end": **********.855687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6f3b10173cc6f5c541f27080145e1a40", "start": **********.859031, "relative_start": 2.4897570610046387, "end": **********.859031, "relative_end": **********.859031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.859677, "relative_start": 2.490403175354004, "end": **********.859677, "relative_end": **********.859677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.861013, "relative_start": 2.49173903465271, "end": **********.861013, "relative_end": **********.861013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.861556, "relative_start": 2.4922821521759033, "end": **********.861556, "relative_end": **********.861556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.861956, "relative_start": 2.4926819801330566, "end": **********.861956, "relative_end": **********.861956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.index", "start": **********.862392, "relative_start": 2.4931180477142334, "end": **********.862392, "relative_end": **********.862392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53362b6227831afe8e4d7d3436ab607f", "start": **********.863621, "relative_start": 2.494347095489502, "end": **********.863621, "relative_end": **********.863621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e76aef074ac8ea84c711b8437720a22", "start": **********.865047, "relative_start": 2.4957730770111084, "end": **********.865047, "relative_end": **********.865047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::04edbddbda254d131a3439b11c880f12", "start": **********.866161, "relative_start": 2.49688720703125, "end": **********.866161, "relative_end": **********.866161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.866886, "relative_start": 2.4976119995117188, "end": **********.866886, "relative_end": **********.866886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::custom-template", "start": **********.867844, "relative_start": 2.498570203781128, "end": **********.867844, "relative_end": **********.867844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::partials.media", "start": **********.868494, "relative_start": 2.499220132827759, "end": **********.868494, "relative_end": **********.868494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.869273, "relative_start": 2.4999990463256836, "end": **********.869273, "relative_end": **********.869273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::loading", "start": **********.869626, "relative_start": 2.500352144241333, "end": **********.869626, "relative_end": **********.869626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.870069, "relative_start": 2.5007951259613037, "end": **********.870069, "relative_end": **********.870069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.870845, "relative_start": 2.5015711784362793, "end": **********.870845, "relative_end": **********.870845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.871303, "relative_start": 2.5020291805267334, "end": **********.871303, "relative_end": **********.871303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.871874, "relative_start": 2.5026001930236816, "end": **********.871874, "relative_end": **********.871874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.checkbox", "start": **********.872666, "relative_start": 2.503391981124878, "end": **********.872666, "relative_end": **********.872666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.874927, "relative_start": 2.505653142929077, "end": **********.874927, "relative_end": **********.874927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.875852, "relative_start": 2.506578207015991, "end": **********.875852, "relative_end": **********.875852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.876613, "relative_start": 2.5073390007019043, "end": **********.876613, "relative_end": **********.876613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.877799, "relative_start": 2.5085251331329346, "end": **********.877799, "relative_end": **********.877799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::config", "start": **********.878477, "relative_start": 2.5092031955718994, "end": **********.878477, "relative_end": **********.878477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::debug-badge", "start": **********.318142, "relative_start": 2.9488680362701416, "end": **********.318142, "relative_end": **********.318142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.31918, "relative_start": 2.94990611076355, "end": **********.31918, "relative_end": **********.31918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.320255, "relative_start": 2.9509811401367188, "end": **********.320255, "relative_end": **********.320255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.321294, "relative_start": 2.9520201683044434, "end": **********.321294, "relative_end": **********.321294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::93ebde3601860db875cfe9a96164bda6", "start": **********.323159, "relative_start": 2.953885078430176, "end": **********.323159, "relative_end": **********.323159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.324147, "relative_start": 2.9548730850219727, "end": **********.324147, "relative_end": **********.324147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.325602, "relative_start": 2.9563281536102295, "end": **********.325602, "relative_end": **********.325602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.326954, "relative_start": 2.9576799869537354, "end": **********.326954, "relative_end": **********.326954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.328089, "relative_start": 2.958815097808838, "end": **********.328089, "relative_end": **********.328089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::16c15d36d71c18d83a7e9e7e1b68a92b", "start": **********.329115, "relative_start": 2.959841012954712, "end": **********.329115, "relative_end": **********.329115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.330008, "relative_start": 2.9607341289520264, "end": **********.330008, "relative_end": **********.330008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::layouts.base", "start": **********.331824, "relative_start": 2.962550163269043, "end": **********.331824, "relative_end": **********.331824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.layouts.header", "start": **********.333175, "relative_start": 2.9639010429382324, "end": **********.333175, "relative_end": **********.333175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.338022, "relative_start": 2.968748092651367, "end": **********.338022, "relative_end": **********.338022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.common", "start": **********.346183, "relative_start": 2.9769091606140137, "end": **********.346183, "relative_end": **********.346183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.349614, "relative_start": 2.980340003967285, "end": **********.349614, "relative_end": **********.349614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.notification", "start": **********.352064, "relative_start": 2.982789993286133, "end": **********.352064, "relative_end": **********.352064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 74610960, "peak_usage_str": "71MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 356, "nb_templates": 356, "templates": [{"name": "1x core/table::table-info", "param_count": null, "params": [], "start": **********.165888, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/table-info.blade.phpcore/table::table-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable-info.blade.php&line=1", "ajax": false, "filename": "table-info.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::table-info"}, {"name": "2x __components::c33cfbd01dd1d76718fcd68287a40728", "param_count": null, "params": [], "start": **********.170905, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/c33cfbd01dd1d76718fcd68287a40728.blade.php__components::c33cfbd01dd1d76718fcd68287a40728", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fc33cfbd01dd1d76718fcd68287a40728.blade.php&line=1", "ajax": false, "filename": "c33cfbd01dd1d76718fcd68287a40728.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::c33cfbd01dd1d76718fcd68287a40728"}, {"name": "1x 8def1252668913628243c4d363bee1ef::badge", "param_count": null, "params": [], "start": **********.176342, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/badge.blade.php8def1252668913628243c4d363bee1ef::badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::badge"}, {"name": "1x core/table::table", "param_count": null, "params": [], "start": **********.182281, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/table.blade.phpcore/table::table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::table"}, {"name": "1x core/table::base-table", "param_count": null, "params": [], "start": **********.182798, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/base-table.blade.phpcore/table::base-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbase-table.blade.php&line=1", "ajax": false, "filename": "base-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::base-table"}, {"name": "1x core/table::bulk-action", "param_count": null, "params": [], "start": **********.187532, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/bulk-action.blade.phpcore/table::bulk-action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbulk-action.blade.php&line=1", "ajax": false, "filename": "bulk-action.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::bulk-action"}, {"name": "5x 8def1252668913628243c4d363bee1ef::dropdown.item", "param_count": null, "params": [], "start": **********.19098, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/dropdown/item.blade.php8def1252668913628243c4d363bee1ef::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::dropdown.item"}, {"name": "3x 8def1252668913628243c4d363bee1ef::dropdown.index", "param_count": null, "params": [], "start": **********.192207, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/dropdown/index.blade.php8def1252668913628243c4d363bee1ef::dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "8def1252668913628243c4d363bee1ef::dropdown.index"}, {"name": "8x 8def1252668913628243c4d363bee1ef::button", "param_count": null, "params": [], "start": **********.193395, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/button.blade.php8def1252668913628243c4d363bee1ef::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 8, "name_original": "8def1252668913628243c4d363bee1ef::button"}, {"name": "2x __components::6f3b10173cc6f5c541f27080145e1a40", "param_count": null, "params": [], "start": **********.196057, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/6f3b10173cc6f5c541f27080145e1a40.blade.php__components::6f3b10173cc6f5c541f27080145e1a40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F6f3b10173cc6f5c541f27080145e1a40.blade.php&line=1", "ajax": false, "filename": "6f3b10173cc6f5c541f27080145e1a40.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::6f3b10173cc6f5c541f27080145e1a40"}, {"name": "1x __components::42e1966f95bce065f65d4b22e53f3772", "param_count": null, "params": [], "start": **********.197412, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/42e1966f95bce065f65d4b22e53f3772.blade.php__components::42e1966f95bce065f65d4b22e53f3772", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F42e1966f95bce065f65d4b22e53f3772.blade.php&line=1", "ajax": false, "filename": "42e1966f95bce065f65d4b22e53f3772.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::42e1966f95bce065f65d4b22e53f3772"}, {"name": "1x __components::0c8728926b3975e33a051ebb6ef68e5d", "param_count": null, "params": [], "start": **********.200475, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0c8728926b3975e33a051ebb6ef68e5d.blade.php__components::0c8728926b3975e33a051ebb6ef68e5d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0c8728926b3975e33a051ebb6ef68e5d.blade.php&line=1", "ajax": false, "filename": "0c8728926b3975e33a051ebb6ef68e5d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0c8728926b3975e33a051ebb6ef68e5d"}, {"name": "3x 8def1252668913628243c4d363bee1ef::card.header.index", "param_count": null, "params": [], "start": **********.201158, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/header/index.blade.php8def1252668913628243c4d363bee1ef::card.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "8def1252668913628243c4d363bee1ef::card.header.index"}, {"name": "3x 8def1252668913628243c4d363bee1ef::card.index", "param_count": null, "params": [], "start": **********.202382, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/index.blade.php8def1252668913628243c4d363bee1ef::card.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "8def1252668913628243c4d363bee1ef::card.index"}, {"name": "1x core/table::modal", "param_count": null, "params": [], "start": **********.203344, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/modal.blade.phpcore/table::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::modal"}, {"name": "6x 8def1252668913628243c4d363bee1ef::modal.action", "param_count": null, "params": [], "start": **********.204973, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal/action.blade.php8def1252668913628243c4d363bee1ef::modal.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 6, "name_original": "8def1252668913628243c4d363bee1ef::modal.action"}, {"name": "6x 8def1252668913628243c4d363bee1ef::modal.alert", "param_count": null, "params": [], "start": **********.20725, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal/alert.blade.php8def1252668913628243c4d363bee1ef::modal.alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 6, "name_original": "8def1252668913628243c4d363bee1ef::modal.alert"}, {"name": "10x 8def1252668913628243c4d363bee1ef::modal.close-button", "param_count": null, "params": [], "start": **********.210235, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal/close-button.blade.php8def1252668913628243c4d363bee1ef::modal.close-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fclose-button.blade.php&line=1", "ajax": false, "filename": "close-button.blade.php", "line": "?"}, "render_count": 10, "name_original": "8def1252668913628243c4d363bee1ef::modal.close-button"}, {"name": "4x __components::d17cb0db54485d707113609802086895", "param_count": null, "params": [], "start": **********.212451, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d17cb0db54485d707113609802086895.blade.php__components::d17cb0db54485d707113609802086895", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd17cb0db54485d707113609802086895.blade.php&line=1", "ajax": false, "filename": "d17cb0db54485d707113609802086895.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::d17cb0db54485d707113609802086895"}, {"name": "10x 8def1252668913628243c4d363bee1ef::modal", "param_count": null, "params": [], "start": **********.213515, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/modal.blade.php8def1252668913628243c4d363bee1ef::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 10, "name_original": "8def1252668913628243c4d363bee1ef::modal"}, {"name": "1x core/table::script", "param_count": null, "params": [], "start": **********.236745, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/table/resources/views/script.blade.phpcore/table::script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::script"}, {"name": "1x core/base::layouts.master", "param_count": null, "params": [], "start": **********.239956, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/master.blade.phpcore/base::layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.master"}, {"name": "1x core/base::layouts.vertical.partials.before-content", "param_count": null, "params": [], "start": **********.245814, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/before-content.blade.phpcore/base::layouts.vertical.partials.before-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fbefore-content.blade.php&line=1", "ajax": false, "filename": "before-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.before-content"}, {"name": "1x core/base::layouts.vertical.partials.header", "param_count": null, "params": [], "start": **********.246518, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/header.blade.phpcore/base::layouts.vertical.partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.header"}, {"name": "2x __components::3a4eb377d01a3c4bb09865b43ffbd313", "param_count": null, "params": [], "start": **********.248348, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/3a4eb377d01a3c4bb09865b43ffbd313.blade.php__components::3a4eb377d01a3c4bb09865b43ffbd313", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F3a4eb377d01a3c4bb09865b43ffbd313.blade.php&line=1", "ajax": false, "filename": "3a4eb377d01a3c4bb09865b43ffbd313.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::3a4eb377d01a3c4bb09865b43ffbd313"}, {"name": "2x core/base::partials.logo", "param_count": null, "params": [], "start": **********.249279, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/partials/logo.blade.phpcore/base::partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::partials.logo"}, {"name": "1x core/base::global-search.navbar-input", "param_count": null, "params": [], "start": **********.264881, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/global-search/navbar-input.blade.phpcore/base::global-search.navbar-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fnavbar-input.blade.php&line=1", "ajax": false, "filename": "navbar-input.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.navbar-input"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.text-input", "param_count": null, "params": [], "start": **********.26566, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/text-input.blade.php8def1252668913628243c4d363bee1ef::form.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.text-input"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.label", "param_count": null, "params": [], "start": **********.267774, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/label.blade.php8def1252668913628243c4d363bee1ef::form.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.label"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.error", "param_count": null, "params": [], "start": **********.268418, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/error.blade.php8def1252668913628243c4d363bee1ef::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.error"}, {"name": "7x 8def1252668913628243c4d363bee1ef::form-group", "param_count": null, "params": [], "start": **********.268925, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form-group.blade.php8def1252668913628243c4d363bee1ef::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 7, "name_original": "8def1252668913628243c4d363bee1ef::form-group"}, {"name": "1x __components::d2cfde89f704c31422aff2fae16ddb81", "param_count": null, "params": [], "start": **********.271234, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d2cfde89f704c31422aff2fae16ddb81.blade.php__components::d2cfde89f704c31422aff2fae16ddb81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd2cfde89f704c31422aff2fae16ddb81.blade.php&line=1", "ajax": false, "filename": "d2cfde89f704c31422aff2fae16ddb81.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d2cfde89f704c31422aff2fae16ddb81"}, {"name": "1x core/base::layouts.partials.theme-toggle", "param_count": null, "params": [], "start": **********.272355, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/theme-toggle.blade.phpcore/base::layouts.partials.theme-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ftheme-toggle.blade.php&line=1", "ajax": false, "filename": "theme-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.theme-toggle"}, {"name": "1x __components::a5645d2a1f3c74251fc89224c575fed8", "param_count": null, "params": [], "start": **********.27563, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a5645d2a1f3c74251fc89224c575fed8.blade.php__components::a5645d2a1f3c74251fc89224c575fed8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa5645d2a1f3c74251fc89224c575fed8.blade.php&line=1", "ajax": false, "filename": "a5645d2a1f3c74251fc89224c575fed8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a5645d2a1f3c74251fc89224c575fed8"}, {"name": "1x core/base::notification.nav-item", "param_count": null, "params": [], "start": **********.27883, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/notification/nav-item.blade.phpcore/base::notification.nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnav-item.blade.php&line=1", "ajax": false, "filename": "nav-item.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.nav-item"}, {"name": "1x __components::639d159f54869d7a8362974885dec505", "param_count": null, "params": [], "start": **********.280239, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/639d159f54869d7a8362974885dec505.blade.php__components::639d159f54869d7a8362974885dec505", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F639d159f54869d7a8362974885dec505.blade.php&line=1", "ajax": false, "filename": "639d159f54869d7a8362974885dec505.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::639d159f54869d7a8362974885dec505"}, {"name": "1x plugins/contact::partials.notification", "param_count": null, "params": [], "start": **********.283787, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/contact/resources/views/partials/notification.blade.phpplugins/contact::partials.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fcontact%2Fresources%2Fviews%2Fpartials%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/contact::partials.notification"}, {"name": "3x __components::cf41524c2db4e8ac4f30aba28550db55", "param_count": null, "params": [], "start": **********.285332, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/cf41524c2db4e8ac4f30aba28550db55.blade.php__components::cf41524c2db4e8ac4f30aba28550db55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fcf41524c2db4e8ac4f30aba28550db55.blade.php&line=1", "ajax": false, "filename": "cf41524c2db4e8ac4f30aba28550db55.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::cf41524c2db4e8ac4f30aba28550db55"}, {"name": "1x 8def1252668913628243c4d363bee1ef::card.title", "param_count": null, "params": [], "start": **********.286616, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/title.blade.php8def1252668913628243c4d363bee1ef::card.title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php&line=1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::card.title"}, {"name": "1x 8def1252668913628243c4d363bee1ef::card.actions", "param_count": null, "params": [], "start": **********.287448, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/card/actions.blade.php8def1252668913628243c4d363bee1ef::card.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::card.actions"}, {"name": "1x plugins/ecommerce::orders.notification", "param_count": null, "params": [], "start": **********.329485, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/orders/notification.blade.phpplugins/ecommerce::orders.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.notification"}, {"name": "1x __components::8394ebb1c2841e3a1166cd3fb0a6e03f", "param_count": null, "params": [], "start": **********.33127, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php__components::8394ebb1c2841e3a1166cd3fb0a6e03f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php&line=1", "ajax": false, "filename": "8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8394ebb1c2841e3a1166cd3fb0a6e03f"}, {"name": "1x core/base::layouts.partials.user-menu", "param_count": null, "params": [], "start": **********.348915, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/user-menu.blade.phpcore/base::layouts.partials.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.user-menu"}, {"name": "2x __components::d059faaba602d6895d68258ab3c890a6", "param_count": null, "params": [], "start": **********.354989, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d059faaba602d6895d68258ab3c890a6.blade.php__components::d059faaba602d6895d68258ab3c890a6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd059faaba602d6895d68258ab3c890a6.blade.php&line=1", "ajax": false, "filename": "d059faaba602d6895d68258ab3c890a6.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d059faaba602d6895d68258ab3c890a6"}, {"name": "2x __components::a3cb4601eb64a80dc01a3c268590a3c8", "param_count": null, "params": [], "start": **********.358875, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a3cb4601eb64a80dc01a3c268590a3c8.blade.php__components::a3cb4601eb64a80dc01a3c268590a3c8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa3cb4601eb64a80dc01a3c268590a3c8.blade.php&line=1", "ajax": false, "filename": "a3cb4601eb64a80dc01a3c268590a3c8.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::a3cb4601eb64a80dc01a3c268590a3c8"}, {"name": "1x core/base::layouts.vertical.partials.aside", "param_count": null, "params": [], "start": **********.360754, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/aside.blade.phpcore/base::layouts.vertical.partials.aside", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Faside.blade.php&line=1", "ajax": false, "filename": "aside.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.aside"}, {"name": "1x core/base::layouts.vertical.partials.sidebar", "param_count": null, "params": [], "start": **********.36677, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/sidebar.blade.phpcore/base::layouts.vertical.partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.sidebar"}, {"name": "1x core/base::layouts.partials.navbar-nav", "param_count": null, "params": [], "start": **********.367204, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/navbar-nav.blade.phpcore/base::layouts.partials.navbar-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav.blade.php&line=1", "ajax": false, "filename": "navbar-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.navbar-nav"}, {"name": "22x core/base::layouts.partials.navbar-nav-item", "param_count": null, "params": [], "start": **********.39313, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/navbar-nav-item.blade.phpcore/base::layouts.partials.navbar-nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item.blade.php", "line": "?"}, "render_count": 22, "name_original": "core/base::layouts.partials.navbar-nav-item"}, {"name": "79x core/base::layouts.partials.navbar-nav-item-link", "param_count": null, "params": [], "start": **********.394759, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/navbar-nav-item-link.blade.phpcore/base::layouts.partials.navbar-nav-item-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item-link.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item-link.blade.php", "line": "?"}, "render_count": 79, "name_original": "core/base::layouts.partials.navbar-nav-item-link"}, {"name": "1x __components::e3b17f7ce9738894b58a8b70b9624457", "param_count": null, "params": [], "start": **********.397163, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e3b17f7ce9738894b58a8b70b9624457.blade.php__components::e3b17f7ce9738894b58a8b70b9624457", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe3b17f7ce9738894b58a8b70b9624457.blade.php&line=1", "ajax": false, "filename": "e3b17f7ce9738894b58a8b70b9624457.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e3b17f7ce9738894b58a8b70b9624457"}, {"name": "1x __components::0c6e6838aa476b78aace81114936689c", "param_count": null, "params": [], "start": **********.401978, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0c6e6838aa476b78aace81114936689c.blade.php__components::0c6e6838aa476b78aace81114936689c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0c6e6838aa476b78aace81114936689c.blade.php&line=1", "ajax": false, "filename": "0c6e6838aa476b78aace81114936689c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0c6e6838aa476b78aace81114936689c"}, {"name": "11x core/base::partials.navbar.badge-count", "param_count": null, "params": [], "start": **********.403271, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/partials/navbar/badge-count.blade.phpcore/base::partials.navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 11, "name_original": "core/base::partials.navbar.badge-count"}, {"name": "11x 8def1252668913628243c4d363bee1ef::navbar.badge-count", "param_count": null, "params": [], "start": **********.40399, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/navbar/badge-count.blade.php8def1252668913628243c4d363bee1ef::navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 11, "name_original": "8def1252668913628243c4d363bee1ef::navbar.badge-count"}, {"name": "1x __components::37fae22c8e215ea2e54e69a5e3a007cc", "param_count": null, "params": [], "start": **********.410325, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/37fae22c8e215ea2e54e69a5e3a007cc.blade.php__components::37fae22c8e215ea2e54e69a5e3a007cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F37fae22c8e215ea2e54e69a5e3a007cc.blade.php&line=1", "ajax": false, "filename": "37fae22c8e215ea2e54e69a5e3a007cc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::37fae22c8e215ea2e54e69a5e3a007cc"}, {"name": "1x __components::8916176d99d4ae2024cd36e11e35b821", "param_count": null, "params": [], "start": **********.415344, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8916176d99d4ae2024cd36e11e35b821.blade.php__components::8916176d99d4ae2024cd36e11e35b821", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8916176d99d4ae2024cd36e11e35b821.blade.php&line=1", "ajax": false, "filename": "8916176d99d4ae2024cd36e11e35b821.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8916176d99d4ae2024cd36e11e35b821"}, {"name": "1x __components::bd27433a6607127acdaf6dc541ab2435", "param_count": null, "params": [], "start": **********.419458, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/bd27433a6607127acdaf6dc541ab2435.blade.php__components::bd27433a6607127acdaf6dc541ab2435", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fbd27433a6607127acdaf6dc541ab2435.blade.php&line=1", "ajax": false, "filename": "bd27433a6607127acdaf6dc541ab2435.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd27433a6607127acdaf6dc541ab2435"}, {"name": "1x __components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "param_count": null, "params": [], "start": **********.424697, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php__components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php&line=1", "ajax": false, "filename": "5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5f4f9ebbae249bdc8b0d599a1ac6ad06"}, {"name": "1x __components::080b92e00b37bcc97c1cd249894494a2", "param_count": null, "params": [], "start": **********.431221, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/080b92e00b37bcc97c1cd249894494a2.blade.php__components::080b92e00b37bcc97c1cd249894494a2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F080b92e00b37bcc97c1cd249894494a2.blade.php&line=1", "ajax": false, "filename": "080b92e00b37bcc97c1cd249894494a2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::080b92e00b37bcc97c1cd249894494a2"}, {"name": "1x __components::6a26943e77184871de1629f41c534094", "param_count": null, "params": [], "start": **********.434827, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/6a26943e77184871de1629f41c534094.blade.php__components::6a26943e77184871de1629f41c534094", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F6a26943e77184871de1629f41c534094.blade.php&line=1", "ajax": false, "filename": "6a26943e77184871de1629f41c534094.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6a26943e77184871de1629f41c534094"}, {"name": "1x __components::8f29f8012139c7a3eb6593c906e1db38", "param_count": null, "params": [], "start": **********.437516, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8f29f8012139c7a3eb6593c906e1db38.blade.php__components::8f29f8012139c7a3eb6593c906e1db38", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8f29f8012139c7a3eb6593c906e1db38.blade.php&line=1", "ajax": false, "filename": "8f29f8012139c7a3eb6593c906e1db38.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8f29f8012139c7a3eb6593c906e1db38"}, {"name": "1x __components::4553f5b37130b2effba490dbdf5419d2", "param_count": null, "params": [], "start": **********.446725, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/4553f5b37130b2effba490dbdf5419d2.blade.php__components::4553f5b37130b2effba490dbdf5419d2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F4553f5b37130b2effba490dbdf5419d2.blade.php&line=1", "ajax": false, "filename": "4553f5b37130b2effba490dbdf5419d2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4553f5b37130b2effba490dbdf5419d2"}, {"name": "1x __components::59c947fc9b2121a5885d4f4e7b1242d8", "param_count": null, "params": [], "start": **********.450402, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/59c947fc9b2121a5885d4f4e7b1242d8.blade.php__components::59c947fc9b2121a5885d4f4e7b1242d8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F59c947fc9b2121a5885d4f4e7b1242d8.blade.php&line=1", "ajax": false, "filename": "59c947fc9b2121a5885d4f4e7b1242d8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::59c947fc9b2121a5885d4f4e7b1242d8"}, {"name": "1x __components::1e9698c460b468bfcaf0f7dbbebf9bf0", "param_count": null, "params": [], "start": **********.453398, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php__components::1e9698c460b468bfcaf0f7dbbebf9bf0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php&line=1", "ajax": false, "filename": "1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1e9698c460b468bfcaf0f7dbbebf9bf0"}, {"name": "2x __components::0d4eb4544a5328bea40b7b01743b8f82", "param_count": null, "params": [], "start": **********.45855, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0d4eb4544a5328bea40b7b01743b8f82.blade.php__components::0d4eb4544a5328bea40b7b01743b8f82", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0d4eb4544a5328bea40b7b01743b8f82.blade.php&line=1", "ajax": false, "filename": "0d4eb4544a5328bea40b7b01743b8f82.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::0d4eb4544a5328bea40b7b01743b8f82"}, {"name": "2x __components::5270fef4db64e6c2fedf42ea8ac88f25", "param_count": null, "params": [], "start": **********.462651, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/5270fef4db64e6c2fedf42ea8ac88f25.blade.php__components::5270fef4db64e6c2fedf42ea8ac88f25", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F5270fef4db64e6c2fedf42ea8ac88f25.blade.php&line=1", "ajax": false, "filename": "5270fef4db64e6c2fedf42ea8ac88f25.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::5270fef4db64e6c2fedf42ea8ac88f25"}, {"name": "1x __components::84f17fac377525e2e49f32058361220b", "param_count": null, "params": [], "start": **********.466125, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/84f17fac377525e2e49f32058361220b.blade.php__components::84f17fac377525e2e49f32058361220b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F84f17fac377525e2e49f32058361220b.blade.php&line=1", "ajax": false, "filename": "84f17fac377525e2e49f32058361220b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::84f17fac377525e2e49f32058361220b"}, {"name": "1x __components::1c8617dee734f51544a3883923ddca6f", "param_count": null, "params": [], "start": **********.471524, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1c8617dee734f51544a3883923ddca6f.blade.php__components::1c8617dee734f51544a3883923ddca6f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1c8617dee734f51544a3883923ddca6f.blade.php&line=1", "ajax": false, "filename": "1c8617dee734f51544a3883923ddca6f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1c8617dee734f51544a3883923ddca6f"}, {"name": "1x __components::3d3bfe5e8598abeb74083f6c26233cb5", "param_count": null, "params": [], "start": **********.478815, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/3d3bfe5e8598abeb74083f6c26233cb5.blade.php__components::3d3bfe5e8598abeb74083f6c26233cb5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F3d3bfe5e8598abeb74083f6c26233cb5.blade.php&line=1", "ajax": false, "filename": "3d3bfe5e8598abeb74083f6c26233cb5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3d3bfe5e8598abeb74083f6c26233cb5"}, {"name": "1x __components::35fe997a7b87ef55d749630606a50a1b", "param_count": null, "params": [], "start": **********.484686, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/35fe997a7b87ef55d749630606a50a1b.blade.php__components::35fe997a7b87ef55d749630606a50a1b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F35fe997a7b87ef55d749630606a50a1b.blade.php&line=1", "ajax": false, "filename": "35fe997a7b87ef55d749630606a50a1b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::35fe997a7b87ef55d749630606a50a1b"}, {"name": "1x __components::a4f1583597dec7e67a8ae044f0915dbe", "param_count": null, "params": [], "start": **********.493655, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a4f1583597dec7e67a8ae044f0915dbe.blade.php__components::a4f1583597dec7e67a8ae044f0915dbe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa4f1583597dec7e67a8ae044f0915dbe.blade.php&line=1", "ajax": false, "filename": "a4f1583597dec7e67a8ae044f0915dbe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a4f1583597dec7e67a8ae044f0915dbe"}, {"name": "1x __components::c0cbd16b0cc2226ec5536610974ba3c3", "param_count": null, "params": [], "start": **********.497587, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/c0cbd16b0cc2226ec5536610974ba3c3.blade.php__components::c0cbd16b0cc2226ec5536610974ba3c3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fc0cbd16b0cc2226ec5536610974ba3c3.blade.php&line=1", "ajax": false, "filename": "c0cbd16b0cc2226ec5536610974ba3c3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c0cbd16b0cc2226ec5536610974ba3c3"}, {"name": "2x __components::70b8df706e60982a72f15e9e2d486203", "param_count": null, "params": [], "start": **********.500673, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/70b8df706e60982a72f15e9e2d486203.blade.php__components::70b8df706e60982a72f15e9e2d486203", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F70b8df706e60982a72f15e9e2d486203.blade.php&line=1", "ajax": false, "filename": "70b8df706e60982a72f15e9e2d486203.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::70b8df706e60982a72f15e9e2d486203"}, {"name": "1x __components::dc78b90963e9d9963376e0e829411cea", "param_count": null, "params": [], "start": **********.504932, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/dc78b90963e9d9963376e0e829411cea.blade.php__components::dc78b90963e9d9963376e0e829411cea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fdc78b90963e9d9963376e0e829411cea.blade.php&line=1", "ajax": false, "filename": "dc78b90963e9d9963376e0e829411cea.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dc78b90963e9d9963376e0e829411cea"}, {"name": "4x __components::7a6e3d0dfcd673b5659893aa4dd54e33", "param_count": null, "params": [], "start": **********.510739, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/7a6e3d0dfcd673b5659893aa4dd54e33.blade.php__components::7a6e3d0dfcd673b5659893aa4dd54e33", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F7a6e3d0dfcd673b5659893aa4dd54e33.blade.php&line=1", "ajax": false, "filename": "7a6e3d0dfcd673b5659893aa4dd54e33.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::7a6e3d0dfcd673b5659893aa4dd54e33"}, {"name": "1x __components::481a833ebeb573258c941c925aa45f7b", "param_count": null, "params": [], "start": **********.51446, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/481a833ebeb573258c941c925aa45f7b.blade.php__components::481a833ebeb573258c941c925aa45f7b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F481a833ebeb573258c941c925aa45f7b.blade.php&line=1", "ajax": false, "filename": "481a833ebeb573258c941c925aa45f7b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::481a833ebeb573258c941c925aa45f7b"}, {"name": "1x __components::b42bb0aa5fceca31ad61711414a614f0", "param_count": null, "params": [], "start": **********.517406, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b42bb0aa5fceca31ad61711414a614f0.blade.php__components::b42bb0aa5fceca31ad61711414a614f0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb42bb0aa5fceca31ad61711414a614f0.blade.php&line=1", "ajax": false, "filename": "b42bb0aa5fceca31ad61711414a614f0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b42bb0aa5fceca31ad61711414a614f0"}, {"name": "3x __components::1c969038219bd5c599f1ca2d81401cea", "param_count": null, "params": [], "start": **********.524542, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1c969038219bd5c599f1ca2d81401cea.blade.php__components::1c969038219bd5c599f1ca2d81401cea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1c969038219bd5c599f1ca2d81401cea.blade.php&line=1", "ajax": false, "filename": "1c969038219bd5c599f1ca2d81401cea.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1c969038219bd5c599f1ca2d81401cea"}, {"name": "1x __components::471e83668278198d730a7a3f4a475d45", "param_count": null, "params": [], "start": **********.530958, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/471e83668278198d730a7a3f4a475d45.blade.php__components::471e83668278198d730a7a3f4a475d45", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F471e83668278198d730a7a3f4a475d45.blade.php&line=1", "ajax": false, "filename": "471e83668278198d730a7a3f4a475d45.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::471e83668278198d730a7a3f4a475d45"}, {"name": "1x __components::d7b40194b2b4ba91a975fc9aafe2d3e8", "param_count": null, "params": [], "start": **********.535706, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d7b40194b2b4ba91a975fc9aafe2d3e8.blade.php__components::d7b40194b2b4ba91a975fc9aafe2d3e8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd7b40194b2b4ba91a975fc9aafe2d3e8.blade.php&line=1", "ajax": false, "filename": "d7b40194b2b4ba91a975fc9aafe2d3e8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7b40194b2b4ba91a975fc9aafe2d3e8"}, {"name": "1x __components::acb69140835a74210411469faeab3034", "param_count": null, "params": [], "start": **********.5458, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/acb69140835a74210411469faeab3034.blade.php__components::acb69140835a74210411469faeab3034", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Facb69140835a74210411469faeab3034.blade.php&line=1", "ajax": false, "filename": "acb69140835a74210411469faeab3034.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::acb69140835a74210411469faeab3034"}, {"name": "1x __components::19cd49cd69455e40dc223df6b4eaf954", "param_count": null, "params": [], "start": **********.550929, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/19cd49cd69455e40dc223df6b4eaf954.blade.php__components::19cd49cd69455e40dc223df6b4eaf954", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F19cd49cd69455e40dc223df6b4eaf954.blade.php&line=1", "ajax": false, "filename": "19cd49cd69455e40dc223df6b4eaf954.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::19cd49cd69455e40dc223df6b4eaf954"}, {"name": "1x __components::2979b72aeeca0047ecdecc3ad66e7e16", "param_count": null, "params": [], "start": **********.55476, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/2979b72aeeca0047ecdecc3ad66e7e16.blade.php__components::2979b72aeeca0047ecdecc3ad66e7e16", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F2979b72aeeca0047ecdecc3ad66e7e16.blade.php&line=1", "ajax": false, "filename": "2979b72aeeca0047ecdecc3ad66e7e16.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2979b72aeeca0047ecdecc3ad66e7e16"}, {"name": "1x __components::3c40febd70fcdc245d99ae7cd02cface", "param_count": null, "params": [], "start": **********.563146, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/3c40febd70fcdc245d99ae7cd02cface.blade.php__components::3c40febd70fcdc245d99ae7cd02cface", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F3c40febd70fcdc245d99ae7cd02cface.blade.php&line=1", "ajax": false, "filename": "3c40febd70fcdc245d99ae7cd02cface.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3c40febd70fcdc245d99ae7cd02cface"}, {"name": "2x __components::90ccac5c8bbb25741ef262bfd81c7551", "param_count": null, "params": [], "start": **********.569192, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/90ccac5c8bbb25741ef262bfd81c7551.blade.php__components::90ccac5c8bbb25741ef262bfd81c7551", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F90ccac5c8bbb25741ef262bfd81c7551.blade.php&line=1", "ajax": false, "filename": "90ccac5c8bbb25741ef262bfd81c7551.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::90ccac5c8bbb25741ef262bfd81c7551"}, {"name": "1x __components::d7ced212b797c29086a7922a858f3070", "param_count": null, "params": [], "start": **********.591002, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d7ced212b797c29086a7922a858f3070.blade.php__components::d7ced212b797c29086a7922a858f3070", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd7ced212b797c29086a7922a858f3070.blade.php&line=1", "ajax": false, "filename": "d7ced212b797c29086a7922a858f3070.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7ced212b797c29086a7922a858f3070"}, {"name": "2x __components::9d41a7757b46012fb4a0d6634d04a1e0", "param_count": null, "params": [], "start": **********.600612, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/9d41a7757b46012fb4a0d6634d04a1e0.blade.php__components::9d41a7757b46012fb4a0d6634d04a1e0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F9d41a7757b46012fb4a0d6634d04a1e0.blade.php&line=1", "ajax": false, "filename": "9d41a7757b46012fb4a0d6634d04a1e0.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::9d41a7757b46012fb4a0d6634d04a1e0"}, {"name": "3x __components::86b7e33bd2198279086ebb1f21c0e2cc", "param_count": null, "params": [], "start": **********.614372, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/86b7e33bd2198279086ebb1f21c0e2cc.blade.php__components::86b7e33bd2198279086ebb1f21c0e2cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F86b7e33bd2198279086ebb1f21c0e2cc.blade.php&line=1", "ajax": false, "filename": "86b7e33bd2198279086ebb1f21c0e2cc.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::86b7e33bd2198279086ebb1f21c0e2cc"}, {"name": "1x __components::8c52d9b1ef0685ec10fdc3e877751e02", "param_count": null, "params": [], "start": **********.629318, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8c52d9b1ef0685ec10fdc3e877751e02.blade.php__components::8c52d9b1ef0685ec10fdc3e877751e02", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8c52d9b1ef0685ec10fdc3e877751e02.blade.php&line=1", "ajax": false, "filename": "8c52d9b1ef0685ec10fdc3e877751e02.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8c52d9b1ef0685ec10fdc3e877751e02"}, {"name": "1x __components::42668be6e8e5266862c6994eaa88bb55", "param_count": null, "params": [], "start": **********.638898, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/42668be6e8e5266862c6994eaa88bb55.blade.php__components::42668be6e8e5266862c6994eaa88bb55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F42668be6e8e5266862c6994eaa88bb55.blade.php&line=1", "ajax": false, "filename": "42668be6e8e5266862c6994eaa88bb55.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::42668be6e8e5266862c6994eaa88bb55"}, {"name": "1x __components::311d8c591d63d3cbd12dceb2fb1ac1c1", "param_count": null, "params": [], "start": **********.647426, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php__components::311d8c591d63d3cbd12dceb2fb1ac1c1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php&line=1", "ajax": false, "filename": "311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::311d8c591d63d3cbd12dceb2fb1ac1c1"}, {"name": "1x __components::613233f0072612a02c74dd1699c0b74c", "param_count": null, "params": [], "start": **********.667238, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/613233f0072612a02c74dd1699c0b74c.blade.php__components::613233f0072612a02c74dd1699c0b74c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F613233f0072612a02c74dd1699c0b74c.blade.php&line=1", "ajax": false, "filename": "613233f0072612a02c74dd1699c0b74c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::613233f0072612a02c74dd1699c0b74c"}, {"name": "1x __components::16225ede2ef5cc17292fd2eb9026fc80", "param_count": null, "params": [], "start": **********.671587, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/16225ede2ef5cc17292fd2eb9026fc80.blade.php__components::16225ede2ef5cc17292fd2eb9026fc80", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F16225ede2ef5cc17292fd2eb9026fc80.blade.php&line=1", "ajax": false, "filename": "16225ede2ef5cc17292fd2eb9026fc80.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::16225ede2ef5cc17292fd2eb9026fc80"}, {"name": "1x __components::46010cb1cb88bb5ead5d94603a4a3d16", "param_count": null, "params": [], "start": **********.681634, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/46010cb1cb88bb5ead5d94603a4a3d16.blade.php__components::46010cb1cb88bb5ead5d94603a4a3d16", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F46010cb1cb88bb5ead5d94603a4a3d16.blade.php&line=1", "ajax": false, "filename": "46010cb1cb88bb5ead5d94603a4a3d16.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::46010cb1cb88bb5ead5d94603a4a3d16"}, {"name": "1x __components::34e3d89351b1208b7f313125eec52879", "param_count": null, "params": [], "start": **********.689719, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/34e3d89351b1208b7f313125eec52879.blade.php__components::34e3d89351b1208b7f313125eec52879", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F34e3d89351b1208b7f313125eec52879.blade.php&line=1", "ajax": false, "filename": "34e3d89351b1208b7f313125eec52879.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::34e3d89351b1208b7f313125eec52879"}, {"name": "1x __components::67034900569133b2c83b32da3dd4f5e5", "param_count": null, "params": [], "start": **********.696462, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/67034900569133b2c83b32da3dd4f5e5.blade.php__components::67034900569133b2c83b32da3dd4f5e5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F67034900569133b2c83b32da3dd4f5e5.blade.php&line=1", "ajax": false, "filename": "67034900569133b2c83b32da3dd4f5e5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::67034900569133b2c83b32da3dd4f5e5"}, {"name": "1x __components::0d623715926c24f9fbc8a4b72c106d5d", "param_count": null, "params": [], "start": **********.719398, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/0d623715926c24f9fbc8a4b72c106d5d.blade.php__components::0d623715926c24f9fbc8a4b72c106d5d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F0d623715926c24f9fbc8a4b72c106d5d.blade.php&line=1", "ajax": false, "filename": "0d623715926c24f9fbc8a4b72c106d5d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0d623715926c24f9fbc8a4b72c106d5d"}, {"name": "1x __components::6c50fc55276d93f8ed03f5c85273b6cc", "param_count": null, "params": [], "start": **********.724664, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/6c50fc55276d93f8ed03f5c85273b6cc.blade.php__components::6c50fc55276d93f8ed03f5c85273b6cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F6c50fc55276d93f8ed03f5c85273b6cc.blade.php&line=1", "ajax": false, "filename": "6c50fc55276d93f8ed03f5c85273b6cc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6c50fc55276d93f8ed03f5c85273b6cc"}, {"name": "1x __components::084d2b43c9ab4b881d9b34a15580aa2d", "param_count": null, "params": [], "start": **********.728448, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/084d2b43c9ab4b881d9b34a15580aa2d.blade.php__components::084d2b43c9ab4b881d9b34a15580aa2d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F084d2b43c9ab4b881d9b34a15580aa2d.blade.php&line=1", "ajax": false, "filename": "084d2b43c9ab4b881d9b34a15580aa2d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::084d2b43c9ab4b881d9b34a15580aa2d"}, {"name": "2x __components::b33d20952e90e5c4a596ff58ad010448", "param_count": null, "params": [], "start": **********.732197, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b33d20952e90e5c4a596ff58ad010448.blade.php__components::b33d20952e90e5c4a596ff58ad010448", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb33d20952e90e5c4a596ff58ad010448.blade.php&line=1", "ajax": false, "filename": "b33d20952e90e5c4a596ff58ad010448.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::b33d20952e90e5c4a596ff58ad010448"}, {"name": "1x __components::7db8cad89359963c1e9aa8fcc6c89817", "param_count": null, "params": [], "start": **********.735203, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/7db8cad89359963c1e9aa8fcc6c89817.blade.php__components::7db8cad89359963c1e9aa8fcc6c89817", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F7db8cad89359963c1e9aa8fcc6c89817.blade.php&line=1", "ajax": false, "filename": "7db8cad89359963c1e9aa8fcc6c89817.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7db8cad89359963c1e9aa8fcc6c89817"}, {"name": "1x __components::325be2a8c3ca3843efa76c03adaee1dc", "param_count": null, "params": [], "start": **********.749472, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/325be2a8c3ca3843efa76c03adaee1dc.blade.php__components::325be2a8c3ca3843efa76c03adaee1dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F325be2a8c3ca3843efa76c03adaee1dc.blade.php&line=1", "ajax": false, "filename": "325be2a8c3ca3843efa76c03adaee1dc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::325be2a8c3ca3843efa76c03adaee1dc"}, {"name": "1x __components::e12a669ffa0346a27198bed32e63b7ba", "param_count": null, "params": [], "start": **********.753681, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e12a669ffa0346a27198bed32e63b7ba.blade.php__components::e12a669ffa0346a27198bed32e63b7ba", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe12a669ffa0346a27198bed32e63b7ba.blade.php&line=1", "ajax": false, "filename": "e12a669ffa0346a27198bed32e63b7ba.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e12a669ffa0346a27198bed32e63b7ba"}, {"name": "1x __components::9d0b20db301db9a47503a93a879bb206", "param_count": null, "params": [], "start": **********.758416, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/9d0b20db301db9a47503a93a879bb206.blade.php__components::9d0b20db301db9a47503a93a879bb206", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F9d0b20db301db9a47503a93a879bb206.blade.php&line=1", "ajax": false, "filename": "9d0b20db301db9a47503a93a879bb206.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9d0b20db301db9a47503a93a879bb206"}, {"name": "1x __components::b985af7bcdacbeac70eaf3979ad19f5a", "param_count": null, "params": [], "start": **********.76149, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b985af7bcdacbeac70eaf3979ad19f5a.blade.php__components::b985af7bcdacbeac70eaf3979ad19f5a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb985af7bcdacbeac70eaf3979ad19f5a.blade.php&line=1", "ajax": false, "filename": "b985af7bcdacbeac70eaf3979ad19f5a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b985af7bcdacbeac70eaf3979ad19f5a"}, {"name": "1x __components::a0bb1d43b71cff86abe626fd376492e9", "param_count": null, "params": [], "start": **********.764217, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/a0bb1d43b71cff86abe626fd376492e9.blade.php__components::a0bb1d43b71cff86abe626fd376492e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fa0bb1d43b71cff86abe626fd376492e9.blade.php&line=1", "ajax": false, "filename": "a0bb1d43b71cff86abe626fd376492e9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a0bb1d43b71cff86abe626fd376492e9"}, {"name": "1x __components::e25f2b305e6de46c04f91fa1ce50f68f", "param_count": null, "params": [], "start": **********.767017, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e25f2b305e6de46c04f91fa1ce50f68f.blade.php__components::e25f2b305e6de46c04f91fa1ce50f68f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe25f2b305e6de46c04f91fa1ce50f68f.blade.php&line=1", "ajax": false, "filename": "e25f2b305e6de46c04f91fa1ce50f68f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e25f2b305e6de46c04f91fa1ce50f68f"}, {"name": "1x __components::69152f707ea1358f8997b77a28e38a6f", "param_count": null, "params": [], "start": **********.770155, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/69152f707ea1358f8997b77a28e38a6f.blade.php__components::69152f707ea1358f8997b77a28e38a6f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F69152f707ea1358f8997b77a28e38a6f.blade.php&line=1", "ajax": false, "filename": "69152f707ea1358f8997b77a28e38a6f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69152f707ea1358f8997b77a28e38a6f"}, {"name": "1x __components::b85eba35d3b7929c2988678b725baebf", "param_count": null, "params": [], "start": **********.775416, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/b85eba35d3b7929c2988678b725baebf.blade.php__components::b85eba35d3b7929c2988678b725baebf", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fb85eba35d3b7929c2988678b725baebf.blade.php&line=1", "ajax": false, "filename": "b85eba35d3b7929c2988678b725baebf.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b85eba35d3b7929c2988678b725baebf"}, {"name": "1x __components::1f3d3b83c612f68036b4d79d53ae851e", "param_count": null, "params": [], "start": **********.779951, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/1f3d3b83c612f68036b4d79d53ae851e.blade.php__components::1f3d3b83c612f68036b4d79d53ae851e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F1f3d3b83c612f68036b4d79d53ae851e.blade.php&line=1", "ajax": false, "filename": "1f3d3b83c612f68036b4d79d53ae851e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1f3d3b83c612f68036b4d79d53ae851e"}, {"name": "1x __components::f143d1296cea16d82e2c87956e445593", "param_count": null, "params": [], "start": **********.784977, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/f143d1296cea16d82e2c87956e445593.blade.php__components::f143d1296cea16d82e2c87956e445593", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Ff143d1296cea16d82e2c87956e445593.blade.php&line=1", "ajax": false, "filename": "f143d1296cea16d82e2c87956e445593.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f143d1296cea16d82e2c87956e445593"}, {"name": "1x __components::8cae4e5056b67c6778a54389a62ac7a0", "param_count": null, "params": [], "start": **********.791078, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/8cae4e5056b67c6778a54389a62ac7a0.blade.php__components::8cae4e5056b67c6778a54389a62ac7a0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F8cae4e5056b67c6778a54389a62ac7a0.blade.php&line=1", "ajax": false, "filename": "8cae4e5056b67c6778a54389a62ac7a0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8cae4e5056b67c6778a54389a62ac7a0"}, {"name": "1x __components::d5e509b6eb9084ec382ec05ccab41d1a", "param_count": null, "params": [], "start": **********.796255, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/d5e509b6eb9084ec382ec05ccab41d1a.blade.php__components::d5e509b6eb9084ec382ec05ccab41d1a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fd5e509b6eb9084ec382ec05ccab41d1a.blade.php&line=1", "ajax": false, "filename": "d5e509b6eb9084ec382ec05ccab41d1a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d5e509b6eb9084ec382ec05ccab41d1a"}, {"name": "1x __components::e81a46563ed9378aa4d9a4fcb55e743e", "param_count": null, "params": [], "start": **********.800473, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/e81a46563ed9378aa4d9a4fcb55e743e.blade.php__components::e81a46563ed9378aa4d9a4fcb55e743e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fe81a46563ed9378aa4d9a4fcb55e743e.blade.php&line=1", "ajax": false, "filename": "e81a46563ed9378aa4d9a4fcb55e743e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e81a46563ed9378aa4d9a4fcb55e743e"}, {"name": "1x __components::da3c3de008e5793cbbdad005d78f49b1", "param_count": null, "params": [], "start": **********.817375, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/da3c3de008e5793cbbdad005d78f49b1.blade.php__components::da3c3de008e5793cbbdad005d78f49b1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2Fda3c3de008e5793cbbdad005d78f49b1.blade.php&line=1", "ajax": false, "filename": "da3c3de008e5793cbbdad005d78f49b1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::da3c3de008e5793cbbdad005d78f49b1"}, {"name": "1x core/base::layouts.partials.page-header", "param_count": null, "params": [], "start": **********.819194, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/page-header.blade.phpcore/base::layouts.partials.page-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fpage-header.blade.php&line=1", "ajax": false, "filename": "page-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.page-header"}, {"name": "1x core/base::breadcrumb", "param_count": null, "params": [], "start": **********.82031, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/breadcrumb.blade.phpcore/base::breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::breadcrumb"}, {"name": "1x core/base::layouts.partials.footer", "param_count": null, "params": [], "start": **********.823738, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/partials/footer.blade.phpcore/base::layouts.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.footer"}, {"name": "1x core/base::partials.copyright", "param_count": null, "params": [], "start": **********.824808, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/partials/copyright.blade.phpcore/base::partials.copyright", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fcopyright.blade.php&line=1", "ajax": false, "filename": "copyright.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::partials.copyright"}, {"name": "1x core/base::layouts.vertical.partials.after-content", "param_count": null, "params": [], "start": **********.826778, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/layouts/vertical/partials/after-content.blade.phpcore/base::layouts.vertical.partials.after-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fafter-content.blade.php&line=1", "ajax": false, "filename": "after-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.after-content"}, {"name": "1x core/base::system.license-invalid", "param_count": null, "params": [], "start": **********.827981, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/system/license-invalid.blade.phpcore/base::system.license-invalid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fsystem%2Flicense-invalid.blade.php&line=1", "ajax": false, "filename": "license-invalid.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::system.license-invalid"}, {"name": "2x 8def1252668913628243c4d363bee1ef::alert", "param_count": null, "params": [], "start": **********.829074, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/alert.blade.php8def1252668913628243c4d363bee1ef::alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::alert"}, {"name": "1x core/base::system.partials.license-activation-modal", "param_count": null, "params": [], "start": **********.8301, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/system/partials/license-activation-modal.blade.phpcore/base::system.partials.license-activation-modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fsystem%2Fpartials%2Flicense-activation-modal.blade.php&line=1", "ajax": false, "filename": "license-activation-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::system.partials.license-activation-modal"}, {"name": "1x 8def1252668913628243c4d363bee1ef::license.form", "param_count": null, "params": [], "start": **********.831834, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/license/form.blade.php8def1252668913628243c4d363bee1ef::license.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flicense%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::license.form"}, {"name": "1x __components::27ec08f706fece52ef1cc0ec5563cef9", "param_count": null, "params": [], "start": **********.835667, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/27ec08f706fece52ef1cc0ec5563cef9.blade.php__components::27ec08f706fece52ef1cc0ec5563cef9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F27ec08f706fece52ef1cc0ec5563cef9.blade.php&line=1", "ajax": false, "filename": "27ec08f706fece52ef1cc0ec5563cef9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::27ec08f706fece52ef1cc0ec5563cef9"}, {"name": "2x 8def1252668913628243c4d363bee1ef::form.helper-text", "param_count": null, "params": [], "start": **********.83957, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/helper-text.blade.php8def1252668913628243c4d363bee1ef::form.helper-text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fhelper-text.blade.php&line=1", "ajax": false, "filename": "helper-text.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::form.helper-text"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "param_count": null, "params": [], "start": **********.846741, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/on-off/checkbox.blade.php8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fon-off%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.on-off.checkbox"}, {"name": "1x core/base::components.form.checkbox", "param_count": null, "params": [], "start": **********.847545, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/checkbox.blade.phpcore/base::components.form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.form.checkbox"}, {"name": "1x ********************************::form-group", "param_count": null, "params": [], "start": **********.850431, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/setting/resources/views/components/form-group.blade.php********************************::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form-group"}, {"name": "1x core/base::global-search.form", "param_count": null, "params": [], "start": **********.855646, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/global-search/form.blade.phpcore/base::global-search.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.form"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.index", "param_count": null, "params": [], "start": **********.862368, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/index.blade.php8def1252668913628243c4d363bee1ef::form.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.index"}, {"name": "1x __components::53362b6227831afe8e4d7d3436ab607f", "param_count": null, "params": [], "start": **********.863595, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/53362b6227831afe8e4d7d3436ab607f.blade.php__components::53362b6227831afe8e4d7d3436ab607f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F53362b6227831afe8e4d7d3436ab607f.blade.php&line=1", "ajax": false, "filename": "53362b6227831afe8e4d7d3436ab607f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::53362b6227831afe8e4d7d3436ab607f"}, {"name": "1x __components::9e76aef074ac8ea84c711b8437720a22", "param_count": null, "params": [], "start": **********.865023, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/9e76aef074ac8ea84c711b8437720a22.blade.php__components::9e76aef074ac8ea84c711b8437720a22", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F9e76aef074ac8ea84c711b8437720a22.blade.php&line=1", "ajax": false, "filename": "9e76aef074ac8ea84c711b8437720a22.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9e76aef074ac8ea84c711b8437720a22"}, {"name": "1x __components::04edbddbda254d131a3439b11c880f12", "param_count": null, "params": [], "start": **********.866139, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/04edbddbda254d131a3439b11c880f12.blade.php__components::04edbddbda254d131a3439b11c880f12", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F04edbddbda254d131a3439b11c880f12.blade.php&line=1", "ajax": false, "filename": "04edbddbda254d131a3439b11c880f12.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::04edbddbda254d131a3439b11c880f12"}, {"name": "1x 8def1252668913628243c4d363bee1ef::custom-template", "param_count": null, "params": [], "start": **********.867822, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/custom-template.blade.php8def1252668913628243c4d363bee1ef::custom-template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcustom-template.blade.php&line=1", "ajax": false, "filename": "custom-template.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::custom-template"}, {"name": "1x core/media::partials.media", "param_count": null, "params": [], "start": **********.868473, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/media/resources/views/partials/media.blade.phpcore/media::partials.media", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fpartials%2Fmedia.blade.php&line=1", "ajax": false, "filename": "media.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::partials.media"}, {"name": "1x 8def1252668913628243c4d363bee1ef::loading", "param_count": null, "params": [], "start": **********.869605, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/loading.blade.php8def1252668913628243c4d363bee1ef::loading", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::loading"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.checkbox", "param_count": null, "params": [], "start": **********.872632, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/form/checkbox.blade.php8def1252668913628243c4d363bee1ef::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.checkbox"}, {"name": "1x core/media::config", "param_count": null, "params": [], "start": **********.878452, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/media/resources/views/config.blade.phpcore/media::config", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fconfig.blade.php&line=1", "ajax": false, "filename": "config.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::config"}, {"name": "1x 8def1252668913628243c4d363bee1ef::debug-badge", "param_count": null, "params": [], "start": **********.318099, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/debug-badge.blade.php8def1252668913628243c4d363bee1ef::debug-badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdebug-badge.blade.php&line=1", "ajax": false, "filename": "debug-badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::debug-badge"}, {"name": "1x __components::93ebde3601860db875cfe9a96164bda6", "param_count": null, "params": [], "start": **********.323116, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/93ebde3601860db875cfe9a96164bda6.blade.php__components::93ebde3601860db875cfe9a96164bda6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F93ebde3601860db875cfe9a96164bda6.blade.php&line=1", "ajax": false, "filename": "93ebde3601860db875cfe9a96164bda6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::93ebde3601860db875cfe9a96164bda6"}, {"name": "1x __components::16c15d36d71c18d83a7e9e7e1b68a92b", "param_count": null, "params": [], "start": **********.329089, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\storage\\framework\\views/16c15d36d71c18d83a7e9e7e1b68a92b.blade.php__components::16c15d36d71c18d83a7e9e7e1b68a92b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fstorage%2Fframework%2Fviews%2F16c15d36d71c18d83a7e9e7e1b68a92b.blade.php&line=1", "ajax": false, "filename": "16c15d36d71c18d83a7e9e7e1b68a92b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::16c15d36d71c18d83a7e9e7e1b68a92b"}, {"name": "1x 8def1252668913628243c4d363bee1ef::layouts.base", "param_count": null, "params": [], "start": **********.331789, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/layouts/base.blade.php8def1252668913628243c4d363bee1ef::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::layouts.base"}, {"name": "1x core/base::components.layouts.header", "param_count": null, "params": [], "start": **********.333144, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/layouts/header.blade.phpcore/base::components.layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.layouts.header"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.337985, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\vendor\\botble\\assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x core/base::elements.common", "param_count": null, "params": [], "start": **********.34614, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/elements/common.blade.phpcore/base::elements.common", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fcommon.blade.php&line=1", "ajax": false, "filename": "common.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.common"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.349576, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\vendor\\botble\\assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x core/base::notification.notification", "param_count": null, "params": [], "start": **********.352027, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/notification/notification.blade.phpcore/base::notification.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.notification"}]}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00511, "accumulated_duration_str": "5.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.0972729, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 10.568}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.1064188, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "martfury", "explain": null, "start_percent": 10.568, "width_percent": 18.591}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.1161768, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 29.159, "width_percent": 7.828}, {"sql": "select count(*) as aggregate from `ec_orders` where (`status` = 'pending' and `is_finished` = 1)", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.312663, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1401", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1401", "ajax": false, "filename": "HookServiceProvider.php", "line": "1401"}, "connection": "martfury", "explain": null, "start_percent": 36.986, "width_percent": 9.198}, {"sql": "select * from `ec_orders` where (`status` = 'pending' and `is_finished` = 1) order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.3146472, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 46.184, "width_percent": 12.524}, {"sql": "select * from `ec_order_addresses` where `type` = 'shipping_address' and `ec_order_addresses`.`order_id` in (72, 73, 74)", "type": "query", "params": [], "bindings": ["shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.319663, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 58.708, "width_percent": 9.785}, {"sql": "select * from `ec_customers` where `ec_customers`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.325913, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 68.493, "width_percent": 17.221}, {"sql": "select count(*) as aggregate from `ec_reviews` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1445}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.487661, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1445", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1445}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1445", "ajax": false, "filename": "HookServiceProvider.php", "line": "1445"}, "connection": "martfury", "explain": null, "start_percent": 85.714, "width_percent": 14.286}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Order": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderAddress": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderAddress.php&line=1", "ajax": false, "filename": "OrderAddress.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 9, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 9}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://martfury.gc/admin/email-logs", "action_name": "email-logs.index", "controller_action": "FriendsOfBotble\\EmailLog\\Http\\Controllers\\EmailLogController@index", "uri": "GET admin/email-logs", "controller": "FriendsOfBotble\\EmailLog\\Http\\Controllers\\EmailLogController@index<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Ffob-email-log%2Fsrc%2FHttp%2FControllers%2FEmailLogController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/email-logs", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Ffob-email-log%2Fsrc%2FHttp%2FControllers%2FEmailLogController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/fob-email-log/src/Http/Controllers/EmailLogController.php:28-33</a>", "middleware": "web, core, auth", "duration": "2.99s", "peak_memory": "74MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-797678536 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-797678536\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1476954037 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1476954037\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">https://martfury.gc/admin/ecommerce/orders/edit/74</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3292 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; ajs_anonymous_id=%22d13d80ea-7fe8-40be-a19b-193267ad504f%22; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; perf_dv6Tr4n=1; botble_cookie_newsletter=1; XSRF-TOKEN=eyJpdiI6ImxMVWFkL0lRcWE3MXZ2VjNua1dzM0E9PSIsInZhbHVlIjoiZ0JYTnIxSUtmbmJ5S0RSWVVCeXYwWHpIUTNqNGc4RWhNSisvc1FacnFkR2d3MGJEQ0Myd3o3a3lIUTJYT3FZUVdQSzFGTlYvTmtZSVBtUWdSYUNWQklPaWRkSkRObElSTVZHOUVKekJEVGVVRE54T3YwWFZ0ZjYvOGRtaXp4a00iLCJtYWMiOiIyY2YzOTg3OGJiMzg2ZDgzYzcxNzA2MDcwMWNjYmE5YzQ3YWYyYWE2ODBmNWEzYmRkYWRlNDBiYjY2Y2EzZjU3IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6ImtpdUpyZ2tYeDVSWkJNMlo3QlFUbWc9PSIsInZhbHVlIjoiOUpHWUU1Z0FLZ1FYRXBIMG9IVzNNMWlGOVFKWDNpeFhGK2J4S1hLUHFVR01ENXhYVmo0YlpkUHhvUWZVSXdNeStjOXIvYWx6LzVRUGxlM2l0WTQ2c3hOakxQVGNBUWNJTDVGVmRrQzhINzA2ZWVzcmdqMUY0UndWMjlZU0k0WTUiLCJtYWMiOiIyMTA4ZTNlZjRkOTM0NzAyZDJkZDBiMWZlY2RkOWU5ODUwNmEyNjExODgwYjk2OWJlOGY1Y2FjNDU1ZTBjYWY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1745886214 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WR19pjDDCaSy1nFQfLF0llRCMr5qDSh1HZpER2CZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xpcSY39d1Zg3R3trr7BnkKsq5KESCQSEoPf4iQQv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1745886214\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-863099654 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Aug 2025 20:48:59 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-863099654\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1244766342 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WR19pjDDCaSy1nFQfLF0llRCMr5qDSh1HZpER2CZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>4</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>6</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>-</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">https://martfury.gc/admin/ecommerce/orders/edit/74</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756414052\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756414052</span></span> {<a class=sf-dump-ref>#4694</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012560000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 28, 2025\n- 00:01:27.646796 from now\nDST Off\">2025-08-28 20:47:32.727821 UTC (+00:00)</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">cod</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1244766342\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://martfury.gc/admin/email-logs", "action_name": "email-logs.index", "controller_action": "FriendsOfBotble\\EmailLog\\Http\\Controllers\\EmailLogController@index"}, "badge": null}}