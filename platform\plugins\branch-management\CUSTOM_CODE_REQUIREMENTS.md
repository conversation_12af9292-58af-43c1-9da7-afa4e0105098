# Custom Code Requirements for Branch Management Plugin

## Overview

This document outlines the custom code that needs to be added to existing files to fully integrate the Branch Management plugin with your Botble CMS installation. These modifications are necessary when the automatic hook integration is not sufficient.

## Required File Modifications

### 1. Checkout Form Integration

**File**: `platform/themes/martfury/views/ecommerce/orders/checkout.blade.php`

**Location**: Add after the shipping methods section

**Code to Add**:
```php
{{-- Branch Management Integration --}}
@if(is_plugin_active('branch-management'))
    @include('plugins/branch-management::checkout.branch-selection')
@endif
```

**Purpose**: Adds branch pickup option to the checkout process

---

### 2. Product Page Integration

**File**: `platform/themes/martfury/views/ecommerce/product.blade.php`

**Location**: Add in the product information section, after product description

**Code to Add**:
```php
{{-- Branch Management Integration --}}
@if(is_plugin_active('branch-management'))
    @include('plugins/branch-management::product.branches', ['product' => $product])
@endif
```

**Purpose**: Displays available branches on product detail pages

---

### 3. Admin Order Details Integration

**File**: `platform/plugins/ecommerce/resources/views/orders/show.blade.php`

**Location**: Add in the shipping address section, after the address display

**Code to Add**:
```php
{{-- Branch Management Integration --}}
@if(is_plugin_active('branch-management'))
    @include('plugins/branch-management::orders.branch-pickup-info', ['order' => $order])
@endif
```

**Purpose**: Shows branch pickup information in admin order details

---

### 4. Customer Order Details Integration

**File**: `platform/themes/martfury/views/ecommerce/orders/detail.blade.php`

**Location**: Add in the order information section, after shipping address

**Code to Add**:
```php
{{-- Branch Management Integration --}}
@if(is_plugin_active('branch-management') && isset($order->shippingAddress) && $order->shippingAddress->is_branch_pickup)
<div class="order-branch-pickup-info mt-3">
    <h5>{{ trans('plugins/branch-management::branch.pickup.title') }}</h5>
    <div class="branch-details">
        <p><strong>{{ $order->shippingAddress->branch_name }}</strong></p>
        <p>{{ $order->shippingAddress->branch_full_address }}</p>
        @if($order->shippingAddress->branch_phone)
            <p><i class="fa fa-phone"></i> {{ $order->shippingAddress->branch_phone }}</p>
        @endif
        @if($order->shippingAddress->branch_pickup_fee > 0)
            <p><i class="fa fa-money-bill"></i> {{ __('Pickup Fee') }}: {{ format_price($order->shippingAddress->branch_pickup_fee) }}</p>
        @endif
    </div>
</div>
@endif
```

**Purpose**: Shows branch pickup information in customer order history

---

### 5. Checkout JavaScript Enhancement

**File**: `platform/themes/martfury/assets/js/checkout.js`

**Location**: Add at the end of the file or in the checkout initialization

**Code to Add**:
```javascript
// Branch Management Integration
$(document).ready(function() {
    // Handle shipping method change for branch pickup
    $(document).on('change', 'input[name="shipping_method"]', function() {
        const branchPickupSection = $('#branch-pickup-section');
        if ($(this).val() === 'branch_pickup') {
            branchPickupSection.show();
            loadBranchesByCity();
        } else {
            branchPickupSection.hide();
            $('#selected-branch-details').hide();
        }
    });

    // Handle city change to reload branches
    $(document).on('change', 'select[name="address[city]"]', function() {
        if ($('input[name="shipping_method"]:checked').val() === 'branch_pickup') {
            loadBranchesByCity();
        }
    });

    // Handle branch selection
    $(document).on('change', '#pickup_branch_id', function() {
        const branchId = $(this).val();
        if (branchId) {
            showBranchDetails(branchId);
            // Trigger shipping fee recalculation
            $('input[name="shipping_method"]:checked').trigger('change');
        } else {
            $('#selected-branch-details').hide();
        }
    });

    function loadBranchesByCity() {
        const cityId = $('select[name="address[city]"]').val();
        const branchSelect = $('#pickup_branch_id');
        
        if (!cityId) return;
        
        $.get('/ajax/branches-by-city', { 
            city_id: cityId, 
            pickup_only: 1 
        }).done(function(branches) {
            branchSelect.empty().append('<option value="">Select a branch...</option>');
            branches.forEach(function(branch) {
                const option = $('<option></option>')
                    .attr('value', branch.id)
                    .attr('data-branch', JSON.stringify(branch))
                    .text(`${branch.name} - ${branch.address}`);
                branchSelect.append(option);
            });
        }).fail(function() {
            console.error('Failed to load branches');
        });
    }

    function showBranchDetails(branchId) {
        const selectedOption = $(`#pickup_branch_id option[value="${branchId}"]`);
        if (!selectedOption.length) return;

        const branchData = JSON.parse(selectedOption.attr('data-branch'));
        const detailsHtml = `
            <div class="row">
                <div class="col-md-6">
                    <strong>Address:</strong><br>
                    ${branchData.full_address}
                </div>
                <div class="col-md-6">
                    <strong>Phone:</strong><br>
                    ${branchData.phone || 'N/A'}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-6">
                    <strong>Pickup Fee:</strong><br>
                    ${branchData.pickup_fee > 0 ? '$' + branchData.pickup_fee : 'Free'}
                </div>
                <div class="col-md-6">
                    <strong>Status:</strong><br>
                    <span class="badge ${branchData.is_open ? 'bg-success' : 'bg-warning'}">
                        ${branchData.is_open ? 'Open' : 'Closed'}
                    </span>
                </div>
            </div>
        `;
        
        $('#branch-info-content').html(detailsHtml);
        $('#selected-branch-details').show();
    }
});
```

**Purpose**: Handles branch selection and displays branch details during checkout

---

## Shipping Method Integration

### 6. Shipping Method Enum Extension

**File**: `platform/plugins/ecommerce/src/Enums/ShippingMethodEnum.php`

**Note**: This is handled automatically by the plugin hooks, but if hooks don't work, add:

```php
public const BRANCH_PICKUP = 'branch_pickup';
```

---

## Database Considerations

### Migration Dependencies

Ensure these migrations run in order:
1. Location plugin migrations (countries, states, cities)
2. Ecommerce plugin migrations (orders, order_addresses)
3. Branch Management plugin migrations

### Foreign Key Constraints

The plugin adds foreign key constraints to existing tables:
- `ec_order_addresses.branch_id` → `branches.id`

## CSS Styling (Optional)

**File**: `platform/themes/martfury/assets/css/style.css`

**Code to Add**:
```css
/* Branch Management Styles */
.branch-pickup-section {
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.branch-details .card {
    border: 1px solid #dee2e6;
}

.branch-card {
    transition: transform 0.2s;
}

.branch-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.operating-hours-wrapper .row {
    border-bottom: 1px solid #f8f9fa;
    padding: 0.5rem 0;
}

.operating-hours-wrapper .row:last-child {
    border-bottom: none;
}

.branch-status .badge {
    margin-right: 0.25rem;
}
```

## Testing Checklist

After implementing the custom code:

### Frontend Tests
- [ ] Branch pickup option appears in checkout
- [ ] Branch selection works correctly
- [ ] Branch details display when selected
- [ ] Pickup fee is calculated properly
- [ ] Branches display on product pages
- [ ] Branch detail pages load correctly
- [ ] Shortcode `[branches-list]` works

### Admin Tests
- [ ] Branch CRUD operations work
- [ ] Operating hours can be set
- [ ] Branch pickup info shows in order details
- [ ] Settings page functions correctly

### Order Flow Tests
- [ ] Orders with branch pickup save correctly
- [ ] Branch information appears in order confirmation
- [ ] Admin can see branch details in order management
- [ ] Customer can see branch info in order history

## Rollback Instructions

If you need to remove the custom code:

1. **Remove added code blocks** from the files listed above
2. **Clear caches**:
   ```bash
   php artisan cache:clear
   php artisan view:clear
   ```
3. **Deactivate plugin** through admin panel
4. **Remove plugin files** if needed

## Version Compatibility

This custom code is compatible with:
- Botble CMS 7.3.0+
- Martfury theme (latest version)
- Location plugin 1.1.6+
- Ecommerce plugin (latest version)

## Support Notes

- Keep a backup of modified files before updates
- Document any additional customizations you make
- Test thoroughly on staging before applying to production
- Monitor error logs after implementation
