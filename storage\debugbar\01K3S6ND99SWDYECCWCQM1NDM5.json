{"__meta": {"id": "01K3S6ND99SWDYECCWCQM1NDM5", "datetime": "2025-08-28 20:42:16", "utime": **********.23495, "method": "POST", "uri": "/cart/add-to-cart", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": 1756413734.167239, "end": **********.234978, "duration": 2.0677390098571777, "duration_str": "2.07s", "measures": [{"label": "Booting", "start": 1756413734.167239, "relative_start": 0, "end": **********.555517, "relative_end": **********.555517, "duration": 1.****************, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.555573, "relative_start": 1.***************, "end": **********.234982, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "679ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.582691, "relative_start": 1.***************, "end": **********.60019, "relative_end": **********.60019, "duration": 0.*****************, "duration_str": "17.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.229155, "relative_start": 2.****************, "end": **********.231495, "relative_end": **********.231495, "duration": 0.0023398399353027344, "duration_str": "2.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 70, "nb_statements": 70, "nb_visible_statements": 70, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.19792000000000004, "accumulated_duration_str": "198ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `ec_products` where `ec_products`.`id` = '30' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["30", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.6689448, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 1.905}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 30 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 97}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6792068, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 1.905, "width_percent": 0.571}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 30 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 101}], "start": **********.684134, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 2.476, "width_percent": 1.122}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 4 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [4, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 101}], "start": **********.6938689, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 3.597, "width_percent": 0.465}, {"sql": "select exists(select * from `ec_options` where `product_id` = 4 and `required` = 1) as `exists`", "type": "query", "params": [], "bindings": [4, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 164}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.701003, "duration": 0.01652, "duration_str": "16.52ms", "memory": 0, "memory_str": null, "filename": "PublicCartController.php:164", "source": {"index": 10, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicCartController.php&line=164", "ajax": false, "filename": "PublicCartController.php", "line": "164"}, "connection": "martfury", "explain": null, "start_percent": 4.062, "width_percent": 8.347}, {"sql": "select `ec_taxes`.`id`, `ec_taxes`.`title`, `ec_taxes`.`percentage` from `ec_tax_products` inner join `ec_taxes` on `ec_taxes`.`id` = `ec_tax_products`.`tax_id` where `ec_tax_products`.`product_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 616}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 205}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.727524, "duration": 0.017839999999999998, "duration_str": "17.84ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:616", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 616}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=616", "ajax": false, "filename": "OrderHelper.php", "line": "616"}, "connection": "martfury", "explain": null, "start_percent": 12.409, "width_percent": 9.014}, {"sql": "select `ec_taxes`.*, `ec_tax_products`.`product_id` as `pivot_product_id`, `ec_tax_products`.`tax_id` as `pivot_tax_id` from `ec_taxes` inner join `ec_tax_products` on `ec_taxes`.`id` = `ec_tax_products`.`tax_id` where `ec_tax_products`.`product_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 534}, {"index": 28, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 622}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 205}], "start": **********.751327, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "martfury", "explain": null, "start_percent": 21.423, "width_percent": 1.299}, {"sql": "select * from `ec_tax_rules` where `ec_tax_rules`.`tax_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 26, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 534}, {"index": 34, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 35, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 622}], "start": **********.7581918, "duration": 0.01325, "duration_str": "13.25ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 22.721, "width_percent": 6.695}, {"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-08-28' and `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["2025-08-28", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": **********.78512, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 29.416, "width_percent": 0.253}, {"sql": "select * from `ec_customers` where `id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 216}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 187}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}], "start": **********.7970781, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 29.669, "width_percent": 0.243}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-08-28 20:42:15' and (`end_date` is null or `end_date` >= '2025-08-28 20:42:15') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` = 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-08-28 20:42:15", "2025-08-28 20:42:15", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 116}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 42}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.8039591, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 29.911, "width_percent": 1.046}, {"sql": "select distinct `ec_product_variations`.`product_id`, `ec_product_variations`.`configurable_product_id`, `ec_product_attributes`.*, `ec_product_attribute_sets`.`title` as `attribute_set_title`, `ec_product_attribute_sets`.`slug` as `attribute_set_slug`, `ec_product_attribute_sets`.`order` as `attribute_set_order` from `ec_product_variations` inner join `ec_product_variation_items` on `ec_product_variation_items`.`variation_id` = `ec_product_variations`.`id` inner join `ec_product_attributes` on `ec_product_attributes`.`id` = `ec_product_variation_items`.`attribute_id` inner join `ec_product_attribute_sets` on `ec_product_attribute_sets`.`id` = `ec_product_attributes`.`attribute_set_id` where `ec_product_variations`.`product_id` = 30 and `ec_product_variations`.`product_id` is not null order by `attribute_set_order` asc", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 578}, {"index": 29, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 647}], "start": **********.8116062, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 30.957, "width_percent": 0.94}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.823952, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 31.897, "width_percent": 0.536}, {"sql": "select distinct `ec_products`.*, `ec_products`.`with_storehouse_management`, `ec_products`.`stock_status`, `ec_products`.`quantity`, `ec_products`.`allow_checkout_when_out_of_stock`, (select count(*) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_count`, (select avg(`ec_reviews`.`star`) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_avg` from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-08-28 20:42:15' OR\nec_products.end_date < '2025-08-28 20:42:15'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-28 20:42:15' AND\nec_products.end_date >= '2025-08-28 20:42:15'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-08-28 20:42:15'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-28 20:42:15' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `ec_products`.`id` in (30) and `ec_products`.`status` = 'published' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) order by\nCASE\nWHEN ec_products.with_storehouse_management = 0 THEN\nCASE WHEN ec_products.stock_status = 'out_of_stock' THEN 1 ELSE 0 END\nELSE\nCASE WHEN ec_products.quantity <= 0 AND ec_products.allow_checkout_when_out_of_stock = 0 THEN 1 ELSE 0 END\nEND ASC\n, `ec_products`.`order` asc, `ec_products`.`created_at` desc", "type": "query", "params": [], "bindings": ["published", "published", 30, "published", 1, "published", "out_of_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.839901, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 32.432, "width_percent": 1.693}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (30) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.847072, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 34.125, "width_percent": 0.738}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (30)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.8514428, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 34.863, "width_percent": 1.101}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (30)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.85721, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 35.964, "width_percent": 1.177}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (30)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.861818, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 37.141, "width_percent": 0.793}, {"sql": "select `ec_products`.*, `ec_product_cross_sale_relations`.`from_product_id` as `pivot_from_product_id`, `ec_product_cross_sale_relations`.`to_product_id` as `pivot_to_product_id`, `ec_product_cross_sale_relations`.`price` as `pivot_price`, `ec_product_cross_sale_relations`.`price_type` as `pivot_price_type`, `ec_product_cross_sale_relations`.`apply_to_all_variations` as `pivot_apply_to_all_variations`, `ec_product_cross_sale_relations`.`is_variant` as `pivot_is_variant` from `ec_products` inner join `ec_product_cross_sale_relations` on `ec_products`.`id` = `ec_product_cross_sale_relations`.`to_product_id` where `ec_product_cross_sale_relations`.`from_product_id` in (30) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.8669229, "duration": 0.03165, "duration_str": "31.65ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 37.935, "width_percent": 15.991}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` in (30)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.9004438, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 53.926, "width_percent": 0.566}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (4) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}], "start": **********.9067142, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 54.492, "width_percent": 1.299}, {"sql": "select `ec_products`.*, `ec_product_cross_sale_relations`.`from_product_id` as `pivot_from_product_id`, `ec_product_cross_sale_relations`.`to_product_id` as `pivot_to_product_id`, `ec_product_cross_sale_relations`.`price` as `pivot_price`, `ec_product_cross_sale_relations`.`price_type` as `pivot_price_type`, `ec_product_cross_sale_relations`.`apply_to_all_variations` as `pivot_apply_to_all_variations`, `ec_product_cross_sale_relations`.`is_variant` as `pivot_is_variant` from `ec_products` inner join `ec_product_cross_sale_relations` on `ec_products`.`id` = `ec_product_cross_sale_relations`.`to_product_id` where `ec_product_cross_sale_relations`.`from_product_id` in (4) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 17}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.914174, "duration": 0.013179999999999999, "duration_str": "13.18ms", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:17", "source": {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=17", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "17"}, "connection": "martfury", "explain": null, "start_percent": 55.79, "width_percent": 6.659}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 1 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.929852, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 62.449, "width_percent": 0.217}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.931605, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 62.667, "width_percent": 0.147}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (24, 25) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.935613, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 62.813, "width_percent": 1.723}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 2 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.9422839, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 64.536, "width_percent": 0.93}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 6 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.945467, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 65.466, "width_percent": 0.571}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.94784, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 66.037, "width_percent": 0.445}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (38) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.9533122, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 66.481, "width_percent": 1.738}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 8 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.96116, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 68.219, "width_percent": 0.824}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.964293, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 69.043, "width_percent": 0.566}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (40, 41) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.9713619, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 69.609, "width_percent": 1.965}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 10 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.97886, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 71.574, "width_percent": 0.657}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.981801, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 72.231, "width_percent": 0.47}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (43) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.9889, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 72.701, "width_percent": 1.708}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 18 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 704}], "start": **********.996884, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 74.409, "width_percent": 0.253}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (18)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.999657, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 74.661, "width_percent": 0.182}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (59, 60, 61) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/EventServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\EventServiceProvider.php", "line": 147}], "start": **********.006712, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 74.843, "width_percent": 0.773}, {"sql": "select distinct `ec_products`.*, `ec_products`.`with_storehouse_management`, `ec_products`.`stock_status`, `ec_products`.`quantity`, `ec_products`.`allow_checkout_when_out_of_stock`, (select count(*) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_count`, (select avg(`ec_reviews`.`star`) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_avg` from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-08-28 20:42:16' OR\nec_products.end_date < '2025-08-28 20:42:16'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-28 20:42:16' AND\nec_products.end_date >= '2025-08-28 20:42:16'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-08-28 20:42:16'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-28 20:42:16' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `status` = 'published' and `ec_products`.`id` in (30) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) order by\nCASE\nWHEN ec_products.with_storehouse_management = 0 THEN\nCASE WHEN ec_products.stock_status = 'out_of_stock' THEN 1 ELSE 0 END\nELSE\nCASE WHEN ec_products.quantity <= 0 AND ec_products.allow_checkout_when_out_of_stock = 0 THEN 1 ELSE 0 END\nEND ASC\n, `ec_products`.`order` asc, `ec_products`.`created_at` desc", "type": "query", "params": [], "bindings": ["published", "published", "published", 30, 1, "published", "out_of_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.026954, "duration": 0.01862, "duration_str": "18.62ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 75.616, "width_percent": 9.408}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (30) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.048043, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 85.024, "width_percent": 0.167}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (30)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.050396, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 85.191, "width_percent": 0.217}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (30)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.0543728, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 85.408, "width_percent": 0.48}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (30)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.059604, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 85.888, "width_percent": 0.47}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 30 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 730}], "start": **********.0643702, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 86.358, "width_percent": 0.157}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 4 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [4, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 730}], "start": **********.068627, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 86.515, "width_percent": 0.52}, {"sql": "select distinct `ec_products`.*, `ec_products`.`with_storehouse_management`, `ec_products`.`stock_status`, `ec_products`.`quantity`, `ec_products`.`allow_checkout_when_out_of_stock`, (select count(*) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_count`, (select avg(`ec_reviews`.`star`) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_avg` from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-08-28 20:42:16' OR\nec_products.end_date < '2025-08-28 20:42:16'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-28 20:42:16' AND\nec_products.end_date >= '2025-08-28 20:42:16'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-08-28 20:42:16'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-08-28 20:42:16' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `ec_products`.`id` in (30) and `ec_products`.`status` = 'published' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) order by\nCASE\nWHEN ec_products.with_storehouse_management = 0 THEN\nCASE WHEN ec_products.stock_status = 'out_of_stock' THEN 1 ELSE 0 END\nELSE\nCASE WHEN ec_products.quantity <= 0 AND ec_products.allow_checkout_when_out_of_stock = 0 THEN 1 ELSE 0 END\nEND ASC\n, `ec_products`.`order` asc, `ec_products`.`created_at` desc", "type": "query", "params": [], "bindings": ["published", "published", 30, "published", 1, "published", "out_of_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.092305, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 87.035, "width_percent": 1.521}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (30) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.096874, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 88.556, "width_percent": 0.192}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (30)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.099293, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 88.748, "width_percent": 0.212}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (30)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.102353, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 88.96, "width_percent": 0.409}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (30)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.106616, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 89.369, "width_percent": 0.667}, {"sql": "select `ec_products`.*, `ec_product_cross_sale_relations`.`from_product_id` as `pivot_from_product_id`, `ec_product_cross_sale_relations`.`to_product_id` as `pivot_to_product_id`, `ec_product_cross_sale_relations`.`price` as `pivot_price`, `ec_product_cross_sale_relations`.`price_type` as `pivot_price_type`, `ec_product_cross_sale_relations`.`apply_to_all_variations` as `pivot_apply_to_all_variations`, `ec_product_cross_sale_relations`.`is_variant` as `pivot_is_variant` from `ec_products` inner join `ec_product_cross_sale_relations` on `ec_products`.`id` = `ec_product_cross_sale_relations`.`to_product_id` where `ec_product_cross_sale_relations`.`from_product_id` in (30) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/helpers/products.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\products.php", "line": 54}], "start": **********.114813, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 90.036, "width_percent": 0.323}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` in (30)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 89}], "start": **********.117167, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 90.36, "width_percent": 0.182}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (4) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "vendor/botble/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 373}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 763}], "start": **********.124244, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 90.542, "width_percent": 0.798}, {"sql": "select `ec_products`.*, `ec_product_cross_sale_relations`.`from_product_id` as `pivot_from_product_id`, `ec_product_cross_sale_relations`.`to_product_id` as `pivot_to_product_id`, `ec_product_cross_sale_relations`.`price` as `pivot_price`, `ec_product_cross_sale_relations`.`price_type` as `pivot_price_type`, `ec_product_cross_sale_relations`.`apply_to_all_variations` as `pivot_apply_to_all_variations`, `ec_product_cross_sale_relations`.`is_variant` as `pivot_is_variant` from `ec_products` inner join `ec_product_cross_sale_relations` on `ec_products`.`id` = `ec_product_cross_sale_relations`.`to_product_id` where `ec_product_cross_sale_relations`.`from_product_id` in (4) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 17}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 239}], "start": **********.133301, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:17", "source": {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=17", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "17"}, "connection": "martfury", "explain": null, "start_percent": 91.34, "width_percent": 0.455}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 1 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 239}], "start": **********.137969, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 91.795, "width_percent": 0.283}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.14076, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 92.078, "width_percent": 0.354}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (24, 25) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.145747, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 92.431, "width_percent": 0.268}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 2 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 239}], "start": **********.1486578, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 92.699, "width_percent": 0.121}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 6 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 239}], "start": **********.150739, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 92.82, "width_percent": 0.273}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.1539419, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 93.093, "width_percent": 0.662}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (38) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.1623871, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 93.755, "width_percent": 0.874}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 8 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 239}], "start": **********.1721148, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 94.629, "width_percent": 0.394}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.176835, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 95.023, "width_percent": 0.546}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (40, 41) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.183764, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 95.569, "width_percent": 0.455}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 10 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 239}], "start": **********.189071, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 96.024, "width_percent": 0.551}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.194616, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 96.574, "width_percent": 0.339}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (43) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.201442, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 96.913, "width_percent": 1.344}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 18 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCartController.php", "line": 239}], "start": **********.210102, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ProductCrossSalePriceService.php:26", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FProductCrossSalePriceService.php&line=26", "ajax": false, "filename": "ProductCrossSalePriceService.php", "line": "26"}, "connection": "martfury", "explain": null, "start_percent": 98.257, "width_percent": 0.5}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` in (18)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.213092, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 98.757, "width_percent": 0.232}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (59, 60, 61) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 27}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/ProductCrossSalePriceService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\ProductCrossSalePriceService.php", "line": 38}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/HandleApplyProductCrossSaleService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\HandleApplyProductCrossSaleService.php", "line": 58}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Cart/Cart.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Cart\\Cart.php", "line": 753}], "start": **********.218617, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 98.989, "width_percent": 1.011}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Product": {"retrieved": 38, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductVariation": {"retrieved": 24, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1", "ajax": false, "filename": "ProductVariation.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Tax": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FTax.php&line=1", "ajax": false, "filename": "Tax.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 71, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 71}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://martfury.gc/cart/add-to-cart", "action_name": "public.cart.add-to-cart", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCartController@store", "uri": "POST cart/add-to-cart", "controller": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCartController@store<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicCartController.php&line=81\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/cart", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicCartController.php&line=81\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/Fronts/PublicCartController.php:81-259</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect, Botble\\Ecommerce\\Http\\Middleware\\CheckCartEnabledMiddleware", "duration": "2.09s", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-727668895 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-727668895\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1007670777 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Qs47vrrLDKPVofyge4BmAfw2fhcGbKEkz8dSYDjZ</span>\"\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">30</span>\"\n  \"<span class=sf-dump-key>qty</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>checkout</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007670777\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-706460455 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Qs47vrrLDKPVofyge4BmAfw2fhcGbKEkz8dSYDjZ</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://martfury.gc/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3292 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; ajs_anonymous_id=%22d13d80ea-7fe8-40be-a19b-193267ad504f%22; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; perf_dv6Tr4n=1; botble_cookie_newsletter=1; XSRF-TOKEN=eyJpdiI6IjUxckpNMlBPMnhISVFOekJqNU9ZakE9PSIsInZhbHVlIjoiYWVqYlpOZ3ptekdVRUhHNHgxMm0zT2RnYllNeWtRMUF6STBlRjZBOXd6eFFLSmxIQ3d6UTY0aTZUanFtd2I1Q01FSEZVSWsyM2hBeUZxZmJHNHArVXNxRE5ZUDE1bHMvWmgyb0VWQUMvMnhPd016TUt5SGRPbnNtZ28zeGVqWmwiLCJtYWMiOiI2YTY2MzViNGY2YjBlODVlYWJhMjViZTEwMWMzMTc2YTBmM2VlM2I5ZTEzNWRhYWU0NDA1YzQ2YWUzZmUxY2M1IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InBPUDl6aXB2eWsxb0dqWEtIVmpLVXc9PSIsInZhbHVlIjoianU3b1RCeHMwSHFTbmE5TGFlT3o1UnI3Nk45QS9LaExUbTRmWll5bURRNDNqYis1Y2M1Mjhtemt2eDZZL2ZCVUw5bUQxakdmWlVxS3pDekg1bFcrUDJSc1NURjEyN3hZWDQ2MmNrRW9WZWtuVnpqVnVmSWorcXdNUjZnUjM2aUEiLCJtYWMiOiI0YjFhNTI4NWMyNzUyNzQ1MmJlOTQzZWU5Y2RmOTE2MDQ4ODI1Y2E4ODk1NWQwNGQ4MTE3OTMwMmQ1OWEwNGE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706460455\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-726030843 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Qs47vrrLDKPVofyge4BmAfw2fhcGbKEkz8dSYDjZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5k3sla4oSukpLrnC9wNGsuBDC4d4iVeu1c9FAQk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726030843\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2743549 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Aug 2025 20:42:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2743549\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-237458621 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Qs47vrrLDKPVofyge4BmAfw2fhcGbKEkz8dSYDjZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>4</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>6</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>-</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://martfury.gc/admin/ecommerce/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref>#4725</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012750000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 28, 2025\n- 00:00:00.196925 from now\nDST Off\">2025-08-28 20:42:16.080993 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4530</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>aa26812c99f0c997f1e301988b86cbb6</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4759</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">aa26812c99f0c997f1e301988b86cbb6</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>30</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>1</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"25 characters\">Red &amp;amp; Black Headphone</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>417.15</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4582</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"14 characters\">products/4.jpg</span>\"\n              \"<span class=sf-dump-key>attributes</span>\" => \"<span class=sf-dump-str title=\"22 characters\">(Color: Blue, Size: S)</span>\"\n              \"<span class=sf-dump-key>taxRate</span>\" => <span class=sf-dump-num>10.0</span>\n              \"<span class=sf-dump-key>taxClasses</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>VAT</span>\" => <span class=sf-dump-num>10.0</span>\n              </samp>]\n              \"<span class=sf-dump-key>options</span>\" => []\n              \"<span class=sf-dump-key>extras</span>\" => []\n              \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SW-129-A0</span>\"\n              \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-num>865.0</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>10.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref>#4779</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012ab0000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 28, 2025\n- 00:00:00.198215 from now\nDST Off\">2025-08-28 20:42:16.080940 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @**********\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @**********</span></span> {<a class=sf-dump-ref>#4736</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012800000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 28, 2025\n- 00:00:00.198396 from now\nDST Off\">2025-08-28 20:42:16.080925 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">a29c6ea575f283afd5a1cd161f599f3a</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-237458621\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://martfury.gc/cart/add-to-cart", "action_name": "public.cart.add-to-cart", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCartController@store"}, "badge": null}}