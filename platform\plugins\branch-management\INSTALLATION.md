# Branch Management Plugin - Installation Guide

## Prerequisites

Before installing the Branch Management plugin, ensure you have:

1. **Botble CMS** version 7.3.0 or higher
2. **Location Plugin** activated and configured
3. **Ecommerce Plugin** activated and configured
4. **PHP 8.1** or higher

## Step-by-Step Installation

### 1. Plugin Installation

1. Copy the `branch-management` folder to `platform/plugins/`
2. Navigate to Admin Panel > Plugins
3. Find "Branch Management" and click "Activate"
4. The plugin will automatically run database migrations

### 2. Verify Installation

Check that the following database tables were created:
- `branches`
- Verify `ec_order_addresses` table has new columns: `branch_id`, `is_branch_pickup`

### 3. Configure Settings

1. Go to Admin Panel > Settings > Branch Management
2. Configure the following options:
   - **Enable Branch Pickup**: Turn on/off pickup functionality
   - **Default Pickup Fee**: Set default fee (can be overridden per branch)
   - **Show on Product Page**: Display branches on product detail pages

### 4. Create Your First Branch

1. Go to Admin Panel > Branches
2. Click "Create New Branch"
3. Fill in the required information:
   - Name (required)
   - Address (required)
   - City (required - must exist in Location plugin)
   - Contact information
   - Operating hours
   - Pickup availability and fee

## Theme Integration

### Automatic Integration (Recommended)

The plugin uses hooks to automatically integrate with most themes. If your theme supports the following hooks, no manual code changes are needed:

- `ecommerce_checkout_form_after_shipping_method_form` (for checkout page branch selection)
- `ecommerce_product_detail_extra_html` (for product page branch availability)

### Manual Integration (If Hooks Not Available)

If the automatic integration doesn't work, add the following code manually:

#### 1. Checkout Page

**File**: `platform/themes/[your-theme]/views/ecommerce/orders/checkout.blade.php`

Add after the shipping methods section:
```php
@if(is_plugin_active('branch-management'))
    @include('plugins/branch-management::checkout.branch-selection')
@endif
```

#### 2. Product Detail Page

**File**: `platform/themes/[your-theme]/views/ecommerce/product.blade.php`

Add in the product information section:
```php
@if(is_plugin_active('branch-management'))
    @include('plugins/branch-management::product.branches', ['product' => $product])
@endif
```

#### 3. Order Details (Admin)

**File**: `platform/plugins/ecommerce/resources/views/orders/show.blade.php`

Add in the shipping information section:
```php
@if(is_plugin_active('branch-management'))
    @include('plugins/branch-management::orders.branch-pickup-info', ['order' => $order])
@endif
```

#### 4. Customer Order Details

**File**: `platform/themes/[your-theme]/views/ecommerce/orders/detail.blade.php`

Add in the order information section:
```php
@if(is_plugin_active('branch-management') && $order->shippingAddress->is_branch_pickup)
<div class="branch-pickup-details">
    <h5>{{ trans('plugins/branch-management::branch.pickup.title') }}</h5>
    <div class="branch-info">
        <p><strong>{{ $order->shippingAddress->branch_name }}</strong></p>
        <p>{{ $order->shippingAddress->branch_full_address }}</p>
        @if($order->shippingAddress->branch_phone)
            <p>{{ __('Phone') }}: {{ $order->shippingAddress->branch_phone }}</p>
        @endif
    </div>
</div>
@endif
```

### 5. JavaScript Integration

**File**: `platform/themes/[your-theme]/assets/js/checkout.js`

Add the branch selection handling code (see README.md for complete code).

## Testing the Installation

### 1. Admin Panel Tests
- [ ] Can access Branches menu
- [ ] Can create a new branch
- [ ] Can edit existing branch
- [ ] Can delete branch
- [ ] Can access settings page

### 2. Frontend Tests
- [ ] Branches display on product pages (if enabled)
- [ ] Branch pickup option appears in checkout
- [ ] Can select branch during checkout
- [ ] Branch information shows in order confirmation
- [ ] Shortcode works: `[branches-list]`

### 3. Order Flow Tests
- [ ] Create order with branch pickup
- [ ] Verify branch info in admin order details
- [ ] Check customer order history shows branch
- [ ] Confirm pickup fee is calculated correctly

## Post-Update Maintenance

After each Botble CMS update:

1. **Check Plugin Status**
   ```bash
   php artisan cms:plugin:list
   ```

2. **Verify Database**
   ```bash
   php artisan migrate:status
   ```

3. **Clear Caches**
   ```bash
   php artisan cache:clear
   php artisan config:clear
   php artisan view:clear
   ```

4. **Re-apply Manual Code** (if used)
   - Check theme files for manual integrations
   - Re-add any custom code that may have been overwritten

5. **Test Functionality**
   - Test branch creation/editing
   - Test checkout with branch pickup
   - Verify order details display correctly

## Backup Recommendations

Before major updates:
1. Backup the `branches` table
2. Backup plugin settings
3. Document any manual theme modifications
4. Test on staging environment first

## Support

For technical support or customization requests:
- Check the troubleshooting section in README.md
- Review the plugin logs in `storage/logs/`
- Contact the development team with specific error messages
