<?php

namespace Bo<PERSON>ble\BranchManagement\Providers;

use Bo<PERSON>ble\Ecommerce\Enums\ShippingMethodEnum;
use Illuminate\Support\ServiceProvider;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        // Register branch pickup as shipping method (like Shippo plugin)
        add_filter(BASE_FILTER_ENUM_ARRAY, function ($values, $class) {
            if ($class == ShippingMethodEnum::class) {
                $values['BRANCH_PICKUP'] = BRANCH_PICKUP_SHIPPING_METHOD_NAME;
            }
            return $values;
        }, 20, 2);

        // Register shipping method label
        add_filter(BASE_FILTER_ENUM_LABEL, function ($value, $class) {
            if ($class == ShippingMethodEnum::class && $value == BRANCH_PICKUP_SHIPPING_METHOD_NAME) {
                return trans('plugins/branch-management::branch.pickup.title');
            }
            return $value;
        }, 20, 2);

        // Handle shipping fee calculation for branch pickup
        add_filter('handle_shipping_fee', [$this, 'handleShippingFee'], 20, 2);

        // Add branch pickup settings to shipping settings page
        add_filter(SHIPPING_METHODS_SETTINGS_PAGE, [$this, 'addShippingSettings'], 20);

        // Handle order creation with branch pickup
        add_action('ecommerce_order_created', [$this, 'handleBranchPickupOrder'], 20, 2);

        // Add branch selection to checkout form
        add_filter('ecommerce_checkout_form_after_shipping_address_form', [$this, 'addBranchSelectionToCheckout'], 20, 2);

        // Add branch pickup info to order displays
        add_filter('ecommerce_order_detail_top', [$this, 'addBranchPickupInfoToOrderDetail'], 20, 2);
        add_filter('ecommerce_order_detail_sidebar_bottom', [$this, 'addBranchPickupInfoToOrderSidebar'], 20, 2);

        // Add branch pickup info to order emails
        add_filter('ecommerce_order_email_variables', [$this, 'addBranchPickupEmailVariables'], 20, 2);

        // Process branch pickup orders after order creation
        add_action('ecommerce_order_placed', [$this, 'handleBranchPickupOrder'], 20, 2);

        // Add branch pickup info to order edit page (using different hook to avoid conflict)
        add_filter('ecommerce_order_edit_wrap_detail_top', [$this, 'addBranchPickupInfoToOrderEdit'], 20, 2);

        // Hook to add branch pickup data to address data before saving
        add_filter('ecommerce_checkout_address_data', [$this, 'addBranchPickupToAddressData'], 20, 2);
    }

    public function handleShippingFee(array $result, array $data): array
    {
        if (!get_ecommerce_setting('branch_pickup_status', 1)) {
            return $result;
        }

        // Always show branch pickup as an option - city/branch selection will be handled in checkout form
        $result[BRANCH_PICKUP_SHIPPING_METHOD_NAME] = [
            'pickup_branch' => [
                'name' => trans('plugins/branch-management::branch.pickup.title'),
                'description' => trans('plugins/branch-management::branch.pickup.checkout_description'),
                'price' => (float) get_ecommerce_setting('branch_pickup_fee', 0),
                'disabled' => false,
                'option' => 'pickup_branch',
            ]
        ];

        return $result;
    }

    public function addShippingSettings(?string $content): string
    {
        return ($content ?? '') . view('plugins/branch-management::settings.shipping')->render();
    }

    public function handleBranchPickupOrder($order, $request = null): void
    {
        // If no request provided, try to get from global request
        if (!$request) {
            $request = request();
        }

        // Use the simplified BranchPickupService to save branch pickup data
        $branchPickupService = app(\Botble\BranchManagement\Services\BranchPickupService::class);
        $branchPickupService->saveBranchPickupToOrder($order, $request);
    }



    public function addBranchSelectionToCheckout(?string $content, $products = null): string
    {
        // $products parameter is required by the hook signature but not used in this implementation

        if (!get_ecommerce_setting('branch_pickup_status', 1)) {
            return $content ?? '';
        }

        // Pre-load cities with pickup branches for server-side rendering
        $branchRepository = app(\Botble\BranchManagement\Repositories\Interfaces\BranchInterface::class);
        $citiesWithBranches = $branchRepository->getCitiesWithPickupBranches();

        $branchSelectionHtml = view('plugins/branch-management::checkout.branch-selection', [
            'citiesWithBranches' => $citiesWithBranches
        ])->render();

        return ($content ?? '') . $branchSelectionHtml;
    }

    public function addBranchPickupInfoToOrderDetail(?string $content, $order): string
    {
        // Use the simplified BranchPickupService to check if this is a branch pickup order
        $branchPickupService = app(\Botble\BranchManagement\Services\BranchPickupService::class);
        if (!$branchPickupService->isBranchPickupOrder($order)) {
            return $content ?? '';
        }

        $branchPickupHtml = view('plugins/branch-management::orders.branch-pickup-info', compact('order'))->render();

        return ($content ?? '') . $branchPickupHtml;
    }

    public function addBranchPickupInfoToOrderSidebar(?string $content, $order): string
    {
        // Use the simplified BranchPickupService to check if this is a branch pickup order
        $branchPickupService = app(\Botble\BranchManagement\Services\BranchPickupService::class);
        if (!$branchPickupService->isBranchPickupOrder($order)) {
            return $content ?? '';
        }

        $branchPickupHtml = view('plugins/branch-management::orders.branch-pickup-sidebar', compact('order'))->render();

        return ($content ?? '') . $branchPickupHtml;
    }

    public function addBranchPickupEmailVariables(array $variables, $order): array
    {
        // Use the simplified BranchPickupService to get branch pickup data
        $branchPickupService = app(\Botble\BranchManagement\Services\BranchPickupService::class);
        $branchInfo = $branchPickupService->getBranchPickupFromOrder($order);

        if (!$branchInfo) {
            return $variables;
        }

        // Create a mock branch object for the email template
        $branch = (object) [
            'name' => $branchInfo['branch_name'],
            'full_address' => $branchInfo['branch_address'],
            'phone' => $branchInfo['branch_phone'],
            'email' => $branchInfo['branch_email'],
            'pickup_fee' => $branchInfo['pickup_fee'],
            'manager_name' => $branchInfo['manager_name'],
            'manager_phone' => $branchInfo['manager_phone'],
            'special_instructions' => $branchInfo['special_instructions'],
        ];

        // Add branch pickup variables to the email variables
        $variables['branch_pickup_info'] = view('plugins/branch-management::emails.branch-pickup-info', compact('order', 'branch'))->render();
        $variables['branch_name'] = $branchInfo['branch_name'];
        $variables['branch_address'] = $branchInfo['branch_address'];
        $variables['branch_phone'] = $branchInfo['branch_phone'];
        $variables['pickup_fee'] = format_price($branchInfo['pickup_fee']);

        // Update shipping method name for email display
        $shippingMethodName = trans('plugins/branch-management::branch.pickup.title') . ': ' . $branchInfo['branch_name'];
        if ($branchInfo['city_name']) {
            $shippingMethodName .= ' -> ' . $branchInfo['city_name'];
        }
        $variables['shipping_method'] = $shippingMethodName;

        return $variables;
    }

    public function addBranchPickupInfoToOrderEdit(?string $content, $order): ?string
    {
        // Use the simplified BranchPickupService to get branch pickup data
        $branchPickupService = app(\Botble\BranchManagement\Services\BranchPickupService::class);
        $branchInfo = $branchPickupService->getBranchPickupFromOrder($order);

        if (!$branchInfo) {
            return $content;
        }

        // Create professional branch info display
        $branchInfoHtml = '<div class="alert alert-info mb-3">';
        $branchInfoHtml .= '<div class="d-flex align-items-center mb-2">';
        $branchInfoHtml .= '<i class="fa fa-store me-2 text-primary"></i>';
        $branchInfoHtml .= '<h5 class="mb-0">' . trans('plugins/branch-management::branch.pickup.title') . ' Details</h5>';
        $branchInfoHtml .= '</div>';

        $branchInfoHtml .= '<div class="row">';

        // Branch Name
        $branchInfoHtml .= '<div class="col-md-3 mb-2">';
        $branchInfoHtml .= '<strong class="text-dark">Branch:</strong><br>';
        $branchInfoHtml .= '<span class="text-primary fw-bold">' . $branchInfo['branch_name'] . '</span>';
        $branchInfoHtml .= '</div>';

        // City
        if ($branchInfo['city_name']) {
            $branchInfoHtml .= '<div class="col-md-3 mb-2">';
            $branchInfoHtml .= '<strong class="text-dark">City:</strong><br>';
            $branchInfoHtml .= '<span class="text-muted">' . $branchInfo['city_name'] . '</span>';
            $branchInfoHtml .= '</div>';
        }

        // Phone
        if ($branchInfo['branch_phone']) {
            $branchInfoHtml .= '<div class="col-md-3 mb-2">';
            $branchInfoHtml .= '<strong class="text-dark">Phone:</strong><br>';
            $branchInfoHtml .= '<a href="tel:' . $branchInfo['branch_phone'] . '" class="text-success text-decoration-none">';
            $branchInfoHtml .= '<i class="fa fa-phone me-1"></i>' . $branchInfo['branch_phone'];
            $branchInfoHtml .= '</a>';
            $branchInfoHtml .= '</div>';
        }

        // Pickup Fee
        if ($branchInfo['pickup_fee'] > 0) {
            $branchInfoHtml .= '<div class="col-md-3 mb-2">';
            $branchInfoHtml .= '<strong class="text-dark">Pickup Fee:</strong><br>';
            $branchInfoHtml .= '<span class="badge bg-info">' . format_price($branchInfo['pickup_fee']) . '</span>';
            $branchInfoHtml .= '</div>';
        }

        $branchInfoHtml .= '</div>';

        // Address (full width)
        if ($branchInfo['branch_address']) {
            $branchInfoHtml .= '<div class="mt-2 pt-2 border-top">';
            $branchInfoHtml .= '<strong class="text-dark">Address:</strong><br>';
            $branchInfoHtml .= '<small class="text-muted">' . $branchInfo['branch_address'] . '</small>';
            $branchInfoHtml .= '</div>';
        }

        $branchInfoHtml .= '</div>';

        return ($content ?? '') . $branchInfoHtml;
    }

    public function addBranchPickupToAddressData(array $addressData, $request): array
    {
        if (!get_ecommerce_setting('branch_pickup_status', 1)) {
            return $addressData;
        }

        // Check if this is a branch pickup order
        $shippingMethod = $request->input('shipping_method');
        $shippingOption = $request->input('shipping_option');
        $branchId = $request->input('pickup_branch_id');

        $isBranchPickup = false;

        // Check shipping method
        if (is_string($shippingMethod) && $shippingMethod === BRANCH_PICKUP_SHIPPING_METHOD_NAME) {
            $isBranchPickup = true;
        } elseif (is_array($shippingMethod) && in_array(BRANCH_PICKUP_SHIPPING_METHOD_NAME, $shippingMethod)) {
            $isBranchPickup = true;
        }

        // Check shipping option as fallback
        if (!$isBranchPickup && is_string($shippingOption) && $shippingOption === 'pickup_branch') {
            $isBranchPickup = true;
        } elseif (!$isBranchPickup && is_array($shippingOption) && in_array('pickup_branch', $shippingOption)) {
            $isBranchPickup = true;
        }

        // If this is a branch pickup order and we have a branch ID, add it to address data
        if ($isBranchPickup && $branchId) {
            $addressData['branch_id'] = (int) $branchId;
            $addressData['is_branch_pickup'] = true;
        }

        return $addressData;
    }

}
