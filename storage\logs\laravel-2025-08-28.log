[2025-08-28 20:46:46] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-28 20:46:51] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-28 20:47:08] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-28 20:47:11] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-28 20:47:11] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-28 20:47:13] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-28 20:47:20] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-28 20:47:33] local.INFO: Branch pickup order processing started {"order_id":74,"shipping_method":{"Botble\\Ecommerce\\Enums\\ShippingMethodEnum":{"value":"branch_pickup","label":"Branch Pickup"}},"shipping_method_string":"branch_pickup","constant_value":"branch_pickup","are_equal":true} 
[2025-08-28 20:47:33] local.INFO: Branch pickup order inputs {"shipping_method":{"2":"branch_pickup"},"shipping_option":{"2":"pickup_branch"},"pickup_branch_id":"1","pickup_city_id":"21"} 
[2025-08-28 20:47:33] local.INFO: Branch pickup check result {"is_branch_pickup":true,"order_shipping_method":{"Botble\\Ecommerce\\Enums\\ShippingMethodEnum":{"value":"branch_pickup","label":"Branch Pickup"}}} 
[2025-08-28 20:47:33] local.INFO: Branch ID extraction result {"order_id":74,"branch_id":"1","pickup_branch_id_input":"1","shipping_option":{"2":"pickup_branch"}} 
[2025-08-28 20:47:33] local.INFO: Shipping address update attempt {"order_id":74,"has_shipping_address":"yes","shipping_address_id":67,"branch_id_to_set":"1","city_id_to_set":"21"} 
[2025-08-28 20:47:33] local.INFO: Before shipping address update {"address_id":67,"current_branch_id":1,"current_is_branch_pickup":true,"update_data":{"branch_id":1,"is_branch_pickup":1}} 
[2025-08-28 20:47:33] local.INFO: Direct update result {"result":true,"fresh_branch_id":1,"fresh_is_branch_pickup":true} 
[2025-08-28 20:47:33] local.INFO: Order shipping method name updated {"order_id":74,"new_shipping_method_name":"Branch Pickup: Downtown Branch -> `Alya'"} 
[2025-08-28 20:47:33] local.INFO: Branch pickup order created {"order_id":74,"branch_id":1,"branch_name":"Downtown Branch","pickup_fee":"0.00"} 
