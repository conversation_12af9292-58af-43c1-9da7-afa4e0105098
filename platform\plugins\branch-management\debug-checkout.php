<?php

/**
 * Debug script to check if branch pickup integration is working
 */

echo "=== Branch Pickup Debug ===\n";

// Check if plugin is active
if (function_exists('is_plugin_active')) {
    $isActive = is_plugin_active('branch-management');
    echo "Plugin active: " . ($isActive ? 'YES' : 'NO') . "\n";
} else {
    echo "Plugin function not available\n";
}

// Check if setting is enabled
if (function_exists('get_ecommerce_setting')) {
    $status = get_ecommerce_setting('branch_pickup_status', 0);
    echo "Branch pickup status: " . $status . "\n";
} else {
    echo "Ecommerce settings function not available\n";
}

// Check if constants are defined
echo "Constants:\n";
echo "- BRANCH_PICKUP_SHIPPING_METHOD_NAME: " . (defined('BRANCH_PICKUP_SHIPPING_METHOD_NAME') ? BRANCH_PICKUP_SHIPPING_METHOD_NAME : 'NOT DEFINED') . "\n";

// Check if hooks are registered
echo "\nHooks:\n";
if (function_exists('has_filter')) {
    echo "- handle_shipping_fee: " . (has_filter('handle_shipping_fee') ? 'YES' : 'NO') . "\n";
    echo "- ecommerce_checkout_form_after_shipping_method_form: " . (has_filter('ecommerce_checkout_form_after_shipping_method_form') ? 'YES' : 'NO') . "\n";
} else {
    echo "Hook functions not available\n";
}

// Check if views exist
echo "\nViews:\n";
$views = [
    'plugins/branch-management::checkout.branch-selection',
];

foreach ($views as $view) {
    if (function_exists('view') && view()->exists($view)) {
        echo "- {$view}: EXISTS\n";
    } else {
        echo "- {$view}: NOT FOUND\n";
    }
}

// Check if routes exist
echo "\nRoutes:\n";
$routes = [
    'public.ajax.branches-by-city',
    'public.ajax.cities-with-branches',
];

foreach ($routes as $route) {
    try {
        if (function_exists('route')) {
            $url = route($route);
            echo "- {$route}: {$url}\n";
        }
    } catch (Exception $e) {
        echo "- {$route}: ERROR - " . $e->getMessage() . "\n";
    }
}

// Test shipping fee handler
echo "\nTesting shipping fee handler:\n";
try {
    if (class_exists('Botble\BranchManagement\Providers\HookServiceProvider')) {
        $provider = new \Botble\BranchManagement\Providers\HookServiceProvider(app());
        $testResult = $provider->handleShippingFee([], []);
        echo "- Shipping fee handler works: YES\n";
        echo "- Result: " . json_encode($testResult) . "\n";
    } else {
        echo "- HookServiceProvider class not found\n";
    }
} catch (Exception $e) {
    echo "- Error testing shipping fee handler: " . $e->getMessage() . "\n";
}

echo "\n=== Debug Complete ===\n";
echo "If any items show as 'NO' or 'NOT FOUND', those need to be fixed.\n";
