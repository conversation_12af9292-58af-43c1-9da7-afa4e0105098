{"__meta": {"id": "01K3S8DQ4FQ2M2PBK9KZAT0R73", "datetime": "2025-08-28 21:13:01", "utime": **********.328618, "method": "GET", "uri": "/ajax/branches-by-city?city_id=21&pickup_only=1", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": 1756415580.402745, "end": **********.328634, "duration": 0.9258890151977539, "duration_str": "926ms", "measures": [{"label": "Booting", "start": 1756415580.402745, "relative_start": 0, "end": **********.217046, "relative_end": **********.217046, "duration": 0.****************, "duration_str": "814ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.217069, "relative_start": 0.***************, "end": **********.328636, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.236703, "relative_start": 0.****************, "end": **********.256753, "relative_end": **********.256753, "duration": 0.***************, "duration_str": "20.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.325422, "relative_start": 0.****************, "end": **********.325935, "relative_end": **********.325935, "duration": 0.0005128383636474609, "duration_str": "513μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0014200000000000003, "accumulated_duration_str": "1.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.2765028, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 39.437}, {"sql": "select * from `branches` where `status` = 'published' and `city_id` = 21 and `is_pickup_available` = 1 order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": ["published", 21, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/branch-management/src/Repositories/Eloquent/BranchRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\branch-management\\src\\Repositories\\Eloquent\\BranchRepository.php", "line": 25}, {"index": 17, "namespace": null, "name": "platform/plugins/branch-management/src/Http/Controllers/Fronts/PublicBranchController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\branch-management\\src\\Http\\Controllers\\Fronts\\PublicBranchController.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.281651, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 39.437, "width_percent": 33.803}, {"sql": "select * from `cities` where `cities`.`id` in (21)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/branch-management/src/Repositories/Eloquent/BranchRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\branch-management\\src\\Repositories\\Eloquent\\BranchRepository.php", "line": 25}, {"index": 23, "namespace": null, "name": "platform/plugins/branch-management/src/Http/Controllers/Fronts/PublicBranchController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\branch-management\\src\\Http\\Controllers\\Fronts\\PublicBranchController.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.28545, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 73.239, "width_percent": 26.761}]}, "models": {"data": {"Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\BranchManagement\\Models\\Branch": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fbranch-management%2Fsrc%2FModels%2FBranch.php&line=1", "ajax": false, "filename": "Branch.php", "line": "?"}}, "Botble\\Location\\Models\\City": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCity.php&line=1", "ajax": false, "filename": "City.php", "line": "?"}}}, "count": 3, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 3}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://martfury.gc/ajax/branches-by-city?city_id=21&pickup_only=1", "action_name": "public.ajax.branches-by-city", "controller_action": "Botble\\BranchManagement\\Http\\Controllers\\Fronts\\PublicBranchController@getBranchesByCity", "uri": "GET ajax/branches-by-city", "controller": "Botble\\BranchManagement\\Http\\Controllers\\Fronts\\PublicBranchController@getBranchesByCity<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fbranch-management%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicBranchController.php&line=35\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\BranchManagement\\Http\\Controllers\\Fronts", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fbranch-management%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicBranchController.php&line=35\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/branch-management/src/Http/Controllers/Fronts/PublicBranchController.php:35-64</a>", "middleware": "web, core", "duration": "923ms", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1661221830 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>city_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">21</span>\"\n  \"<span class=sf-dump-key>pickup_only</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1661221830\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1932090406 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1932090406\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-480199987 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"61 characters\">https://martfury.gc/checkout/9e9194e021b387ddb80b6dd2c5a0b70c</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3292 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; ajs_anonymous_id=%22d13d80ea-7fe8-40be-a19b-193267ad504f%22; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; perf_dv6Tr4n=1; botble_cookie_newsletter=1; XSRF-TOKEN=eyJpdiI6IldlRE9EaUtCdkkyZlJEL0hkN1ZaVWc9PSIsInZhbHVlIjoiS3o0UkxPZCt6STJJaDN2QkxJcWttbGorRU1obE1kRGZMcHRQSDd5M1plbXRzRlRBZ3JTeldXa0FiclZCK2RyZEhsYjdEZ3ZYdGpKbjR5WUNUTmw3aXBKSlE0QzM1NXlXVGlIUGVRanBhbTc3Qk5qNENseEthSjBqWGlVeWJIdHIiLCJtYWMiOiIyMzU1NTg2ZTBmNjhjMjYwYzA0YjcwMTNlNDE0Yjk4ZDgwMDA1Y2Y3OTVlZGRiOGJjZDQ5MDcxOTFmNDczZTA1IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IlAvMFlSUHdJN3d1YXNkK2JEMlhDeGc9PSIsInZhbHVlIjoieHFtaElxbk1EL1JyRkhzVnRER2oxZEpKcVJqVStmMXA4Njh4S2VoZENEVXpyeFJTZ0hZL3pnS0VrRFptV3BDQ1dVWW9aeVBBdWR1MVRPS3NZRVYxcDNWV0xvTWpHN0RUVE92TzBubVdpQXFBdnRXa0VoM2piZEFJS2ZBbmVORHEiLCJtYWMiOiIzMWJmYTI5M2U2YmUwNDgwNTVmNzQzNmY2YjgwMzVjMWM1ODY0ODZkNDA4YmY0NzUyMmVmM2Q4YmQ0NDAyZGNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-480199987\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1165714077 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WR19pjDDCaSy1nFQfLF0llRCMr5qDSh1HZpER2CZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xpcSY39d1Zg3R3trr7BnkKsq5KESCQSEoPf4iQQv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165714077\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1111277259 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 28 Aug 2025 21:13:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1111277259\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1160115714 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WR19pjDDCaSy1nFQfLF0llRCMr5qDSh1HZpER2CZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>4</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>6</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>-</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"66 characters\">https://martfury.gc/ajax/branches-by-city?city_id=21&amp;pickup_only=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756415569\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756415569</span></span> {<a class=sf-dump-ref href=#sf-dump-1160115714-ref24729 title=\"3 occurrences\">#4729</a><samp data-depth=3 id=sf-dump-1160115714-ref24729 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012790000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 28, 2025\n- 00:00:11.64054 from now\nDST Off\">2025-08-28 21:12:49.693222 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4730</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>9195e402ea9bcd8964b1d4b8df8ff50c</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4731</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">9195e402ea9bcd8964b1d4b8df8ff50c</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>40</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>1</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"17 characters\">Smart Televisions</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>915.04</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4732</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"14 characters\">products/8.jpg</span>\"\n              \"<span class=sf-dump-key>attributes</span>\" => \"<span class=sf-dump-str title=\"23 characters\">(Color: Red, Size: XXL)</span>\"\n              \"<span class=sf-dump-key>taxRate</span>\" => <span class=sf-dump-num>10.0</span>\n              \"<span class=sf-dump-key>taxClasses</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>VAT</span>\" => <span class=sf-dump-num>10.0</span>\n              </samp>]\n              \"<span class=sf-dump-key>options</span>\" => []\n              \"<span class=sf-dump-key>extras</span>\" => []\n              \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SW-150-A0</span>\"\n              \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-num>599.0</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>10.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756415569\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756415569</span></span> {<a class=sf-dump-ref>#4733</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000127d0000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 28, 2025\n- 00:00:11.641066 from now\nDST Off\">2025-08-28 21:12:49.693173 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756415569\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756415569</span></span> {<a class=sf-dump-ref>#4734</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000127e0000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Thursday, August 28, 2025\n- 00:00:11.641142 from now\nDST Off\">2025-08-28 21:12:49.693156 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">cod</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">9e9194e021b387ddb80b6dd2c5a0b70c</span>\"\n  \"<span class=sf-dump-key>2ea92f6e13b9fbe3168b42df02f1e1fc</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>address_id</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n    \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:26</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>address_id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n        \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n        \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n        \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n        \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n        \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756415569\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756415569</span></span> {<a class=sf-dump-ref href=#sf-dump-1160115714-ref24729 title=\"3 occurrences\">#4729</a>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>75</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address_id</span>\" => <span class=sf-dump-num>68</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756415569\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756415569</span></span> {<a class=sf-dump-ref href=#sf-dump-1160115714-ref24729 title=\"3 occurrences\">#4729</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">branch_pickup</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Free delivery</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>branch_pickup</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>pickup_branch</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Branch Pickup</span>\"\n              \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Please select your preferred pickup location</span>\"\n              \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>0.0</span>\n              \"<span class=sf-dump-key>disabled</span>\" => <span class=sf-dump-const>false</span>\n              \"<span class=sf-dump-key>option</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">branch_pickup</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pickup_branch</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160115714\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://martfury.gc/ajax/branches-by-city?city_id=21&pickup_only=1", "action_name": "public.ajax.branches-by-city", "controller_action": "Botble\\BranchManagement\\Http\\Controllers\\Fronts\\PublicBranchController@getBranchesByCity"}, "badge": null}}