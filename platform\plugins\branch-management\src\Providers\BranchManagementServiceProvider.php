<?php

namespace Bo<PERSON><PERSON>\BranchManagement\Providers;

use Bo<PERSON>ble\Base\Facades\DashboardMenu;
use Bo<PERSON>ble\Base\Facades\PanelSectionManager;
use Bo<PERSON>ble\Base\PanelSections\PanelSectionItem;
use Botble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Botble\BranchManagement\Models\Branch;
use Bo<PERSON>ble\BranchManagement\Repositories\Eloquent\BranchRepository;
use Bo<PERSON>ble\BranchManagement\Repositories\Interfaces\BranchInterface;
use Bo<PERSON>ble\BranchManagement\Services\OrderAddressExtensionService;
use Bo<PERSON>ble\Ecommerce\Enums\ShippingMethodEnum;
use Bo<PERSON>ble\Ecommerce\PanelSections\SettingEcommercePanelSection;
use Bo<PERSON>ble\Theme\Facades\Theme;
use Illuminate\Routing\Events\RouteMatched;
use Botble\Base\Facades\Html;
use Bo<PERSON>ble\BranchManagement\Providers\EventServiceProvider;

class BranchManagementServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(BranchInterface::class, function () {
            return new BranchRepository(new Branch());
        });

        $this->app->register(HookServiceProvider::class);
        $this->app->register(EventServiceProvider::class);
    }

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/branch-management')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions', 'email'])
            ->loadMigrations()
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes();

        $this->app['events']->listen(RouteMatched::class, function (): void {
            DashboardMenu::registerItem([
                'id' => 'cms-plugins-branch-management',
                'priority' => 5,
                'parent_id' => null,
                'name' => 'plugins/branch-management::branch.name',
                'icon' => 'ti ti-building-store',
                'url' => route('branches.index'),
                'permissions' => ['branches.index'],
            ]);
        });

        $this->app->booted(function (): void {
            $this->registerShortcodes();
            $this->registerHooks();
            $this->extendOrderAddress();
        });

        // Register settings in Ecommerce panel section
        PanelSectionManager::beforeRendering(function (): void {
            if (is_plugin_active('ecommerce')) {
                PanelSectionManager::default()->registerItem(
                    SettingEcommercePanelSection::class,
                    fn () => PanelSectionItem::make('settings.ecommerce.branch_management')
                        ->setTitle(trans('plugins/branch-management::branch.name'))
                        ->withIcon('ti ti-building-store')
                        ->withDescription(trans('plugins/branch-management::settings.description'))
                        ->withPriority(160)
                        ->withRoute('branch-management.settings.edit')
                );
            }
        });
    }

    protected function registerShortcodes(): void
    {
        if (! is_plugin_active('shortcode')) {
            return;
        }

        add_shortcode('branches-list', __('Branches List'), __('Display all branches grouped by city'), function ($shortcode) {
            return view('plugins/branch-management::shortcodes.branches-list', [
                'city_id' => $shortcode->city_id ?? null,
                'limit' => $shortcode->limit ?? 10,
            ])->render();
        });
    }

    protected function registerHooks(): void
    {
        // Register hook for product page branch display (exactly like quote plugin)
        add_filter(ECOMMERCE_PRODUCT_DETAIL_EXTRA_HTML, [$this, 'addBranchesToProduct'], 120, 2);

        // Load assets for frontend
        add_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, [$this, 'loadProductPageAssets'], 120, 2);

        // Load assets for checkout page
        add_filter('ecommerce_checkout_header', [$this, 'loadCheckoutAssets'], 120);
    }

    public function addBranchesToProduct(?string $content, $product): string
    {
        if (!get_ecommerce_setting('branch_pickup_status', 1)) {
            return $content ?? '';
        }

        $branchView = view('plugins/branch-management::product.branches', compact('product'))->render();
        return ($content ?? '') . $branchView;
    }

    public function loadProductPageAssets($screen, $object): void
    {
        if ($screen !== PRODUCT_MODULE_SCREEN_NAME) {
            return;
        }

        if (!get_ecommerce_setting('branch_pickup_status', 1)) {
            return;
        }

        // Load CSS
        Theme::asset()
            ->usePath(false)
            ->add('branch-management-css', asset('vendor/core/plugins/branch-management/css/branch-management.css'), [], [], '1.0.0');

        // Load JS
        Theme::asset()
            ->container('footer')
            ->usePath(false)
            ->add(
                'branch-product-js',
                asset('vendor/core/plugins/branch-management/js/branch-product.js'),
                ['jquery'],
                [],
                '1.0.0'
            );
    }

    public function loadCheckoutAssets(?string $html): string
    {
        if (!get_ecommerce_setting('branch_pickup_status', 1)) {
            return $html ?? '';
        }

        // Add CSS
        $css = Html::style('vendor/core/plugins/branch-management/css/branch-management.css?v=' . time());

        // Add JS
        $js = Html::script('vendor/core/plugins/branch-management/js/branch-checkout.js?v=' . time());

        return ($html ?? '') . $css . $js;
    }

    protected function extendOrderAddress(): void
    {
        if (is_plugin_active('ecommerce') && OrderAddressExtensionService::checkDatabaseColumns()) {
            OrderAddressExtensionService::extendOrderAddressModel();
        }
    }
}
