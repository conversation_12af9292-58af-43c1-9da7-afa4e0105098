# Manual Integration Guide - Branch Management

## 🚨 **Quick Fix for Immediate Testing**

If the hooks are not working automatically, add this code manually to test:

### 1. **Product Page Integration**

**File**: `platform/themes/martfury/views/ecommerce/product.blade.php`

**Find this line** (around line 126):
```php
{!! apply_filters(ECOMMERCE_PRODUCT_DETAIL_EXTRA_HTML, null, $product) !!}
```

**Replace with**:
```php
{!! apply_filters(ECOMMERCE_PRODUCT_DETAIL_EXTRA_HTML, null, $product) !!}

{{-- Branch Management Manual Integration --}}
@if(is_plugin_active('branch-management'))
    @include('plugins/branch-management::product.branches', ['product' => $product])
@endif
```

### 2. **Checkout Page Integration**

**File**: `platform/plugins/ecommerce/src/Forms/Fronts/CheckoutForm.php`

**Find this line** (around line 207):
```php
HtmlFieldOption::make()->content(apply_filters('ecommerce_checkout_form_after_shipping_method_form', null, $model['products']))
```

**Replace with**:
```php
HtmlFieldOption::make()->content(
    apply_filters('ecommerce_checkout_form_after_shipping_method_form', null, $model['products']) .
    (is_plugin_active('branch-management') ? view('plugins/branch-management::checkout.branch-selection')->render() : '')
)
```

## 🔧 **Alternative: Theme Override Method**

### Create Custom Checkout Template

1. **Create directory**: `platform/themes/martfury/views/ecommerce/orders/`

2. **Copy file**: Copy `platform/plugins/ecommerce/resources/views/orders/checkout.blade.php` to the new directory

3. **Modify the copied file** and add after the shipping methods section:
```php
{!! $checkoutForm->renderForm() !!}

{{-- Branch Management Integration --}}
@if(is_plugin_active('branch-management'))
    @include('plugins/branch-management::checkout.branch-selection')
@endif
```

## 🧪 **Testing Steps**

### 1. **Test Product Page**
1. Go to any product page
2. You should see a "Branch Pickup" section
3. Select a city from dropdown
4. Branches should load dynamically

### 2. **Test Checkout**
1. Add product to cart
2. Go to checkout
3. Fill shipping address
4. You should see branch pickup section
5. Select "Branch Pickup" shipping method
6. Select a branch from dropdown

### 3. **Test Settings**
1. Go to Admin Panel > Settings > Ecommerce
2. Look for "Branch Management" option
3. Configure the settings

## 🐛 **Troubleshooting**

### If Product Page Not Showing Branches:
```php
// Add this to test in product.blade.php
@if(is_plugin_active('branch-management'))
    <div class="alert alert-info">Branch Management Plugin is Active</div>
    @php
        $branchRepository = app(\Botble\BranchManagement\Repositories\Interfaces\BranchInterface::class);
        $branches = $branchRepository->getFeaturedBranches(5);
        echo "Found " . $branches->count() . " branches";
    @endphp
@else
    <div class="alert alert-warning">Branch Management Plugin is NOT Active</div>
@endif
```

### If Checkout Not Showing Branch Pickup:
```php
// Add this to test in checkout form
@if(get_ecommerce_setting('branch_management_enable_pickup', true))
    <div class="alert alert-success">Branch Pickup is Enabled</div>
@else
    <div class="alert alert-danger">Branch Pickup is Disabled</div>
@endif
```

### Check Plugin Status:
```bash
php artisan cms:plugin:list
```

### Check Settings:
```php
// In any blade file, add this to debug:
@php
    echo "Enable Pickup: " . (get_ecommerce_setting('branch_management_enable_pickup', true) ? 'Yes' : 'No') . "<br>";
    echo "Show on Product: " . (get_ecommerce_setting('branch_management_show_on_product_page', true) ? 'Yes' : 'No') . "<br>";
    echo "Plugin Active: " . (is_plugin_active('branch-management') ? 'Yes' : 'No') . "<br>";
@endphp
```

## 📝 **Quick Test Commands**

```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# Check if plugin is active
php artisan cms:plugin:list | grep branch

# Check database
php artisan migrate:status | grep branch
```

## 🎯 **Expected Results**

After manual integration:

1. **Product Page**: Should show branch selection with city dropdown
2. **Checkout**: Should show branch pickup as shipping option
3. **Settings**: Should appear in Ecommerce settings section
4. **Orders**: Should save branch information when pickup is selected

Try the manual integration first, then we can debug why the hooks aren't working automatically!
