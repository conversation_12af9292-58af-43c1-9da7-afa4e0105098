(()=>{var t={3794:()=>{[].slice.call(document.querySelectorAll('[data-bs-toggle="switch-icon"]')).map((function(t){t.addEventListener("click",(e=>{e.stopPropagation(),t.classList.toggle("active")}))}))},5947:function(t,e,n){var i,s;
/* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
 * @license MIT */i=function(){var t,e,n={version:"0.2.0"},i=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function s(t,e,n){return t<e?e:t>n?n:t}function o(t){return 100*(-1+t)}function r(t,e,n){var s;return(s="translate3d"===i.positionUsing?{transform:"translate3d("+o(t)+"%,0,0)"}:"translate"===i.positionUsing?{transform:"translate("+o(t)+"%,0)"}:{"margin-left":o(t)+"%"}).transition="all "+e+"ms "+n,s}n.configure=function(t){var e,n;for(e in t)void 0!==(n=t[e])&&t.hasOwnProperty(e)&&(i[e]=n);return this},n.status=null,n.set=function(t){var e=n.isStarted();t=s(t,i.minimum,1),n.status=1===t?null:t;var o=n.render(!e),c=o.querySelector(i.barSelector),u=i.speed,h=i.easing;return o.offsetWidth,a((function(e){""===i.positionUsing&&(i.positionUsing=n.getPositioningCSS()),l(c,r(t,u,h)),1===t?(l(o,{transition:"none",opacity:1}),o.offsetWidth,setTimeout((function(){l(o,{transition:"all "+u+"ms linear",opacity:0}),setTimeout((function(){n.remove(),e()}),u)}),u)):setTimeout(e,u)})),this},n.isStarted=function(){return"number"==typeof n.status},n.start=function(){n.status||n.set(0);var t=function(){setTimeout((function(){n.status&&(n.trickle(),t())}),i.trickleSpeed)};return i.trickle&&t(),this},n.done=function(t){return t||n.status?n.inc(.3+.5*Math.random()).set(1):this},n.inc=function(t){var e=n.status;return e?("number"!=typeof t&&(t=(1-e)*s(Math.random()*e,.1,.95)),e=s(e+t,0,.994),n.set(e)):n.start()},n.trickle=function(){return n.inc(Math.random()*i.trickleRate)},t=0,e=0,n.promise=function(i){return i&&"resolved"!==i.state()?(0===e&&n.start(),t++,e++,i.always((function(){0===--e?(t=0,n.done()):n.set((t-e)/t)})),this):this},n.render=function(t){if(n.isRendered())return document.getElementById("nprogress");u(document.documentElement,"nprogress-busy");var e=document.createElement("div");e.id="nprogress",e.innerHTML=i.template;var s,r=e.querySelector(i.barSelector),a=t?"-100":o(n.status||0),c=document.querySelector(i.parent);return l(r,{transition:"all 0 linear",transform:"translate3d("+a+"%,0,0)"}),i.showSpinner||(s=e.querySelector(i.spinnerSelector))&&f(s),c!=document.body&&u(c,"nprogress-custom-parent"),c.appendChild(e),e},n.remove=function(){h(document.documentElement,"nprogress-busy"),h(document.querySelector(i.parent),"nprogress-custom-parent");var t=document.getElementById("nprogress");t&&f(t)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var t=document.body.style,e="WebkitTransform"in t?"Webkit":"MozTransform"in t?"Moz":"msTransform"in t?"ms":"OTransform"in t?"O":"";return e+"Perspective"in t?"translate3d":e+"Transform"in t?"translate":"margin"};var a=function(){var t=[];function e(){var n=t.shift();n&&n(e)}return function(n){t.push(n),1==t.length&&e()}}(),l=function(){var t=["Webkit","O","Moz","ms"],e={};function n(t){return t.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,(function(t,e){return e.toUpperCase()}))}function i(e){var n=document.body.style;if(e in n)return e;for(var i,s=t.length,o=e.charAt(0).toUpperCase()+e.slice(1);s--;)if((i=t[s]+o)in n)return i;return e}function s(t){return t=n(t),e[t]||(e[t]=i(t))}function o(t,e,n){e=s(e),t.style[e]=n}return function(t,e){var n,i,s=arguments;if(2==s.length)for(n in e)void 0!==(i=e[n])&&e.hasOwnProperty(n)&&o(t,n,i);else o(t,s[1],s[2])}}();function c(t,e){return("string"==typeof t?t:d(t)).indexOf(" "+e+" ")>=0}function u(t,e){var n=d(t),i=n+e;c(n,e)||(t.className=i.substring(1))}function h(t,e){var n,i=d(t);c(t,e)&&(n=i.replace(" "+e+" "," "),t.className=n.substring(1,n.length-1))}function d(t){return(" "+(t.className||"")+" ").replace(/\s+/gi," ")}function f(t){t&&t.parentNode&&t.parentNode.removeChild(t)}return n},void 0===(s="function"==typeof i?i.call(e,n,e,t):i)||(t.exports=s)},8528:()=>{const t=document.querySelectorAll('[data-bs-toggle="autosize"]');t.length&&t.forEach((function(t){window.autosize&&window.autosize(t)}))}},e={};function n(i){var s=e[i];if(void 0!==s)return s.exports;var o=e[i]={exports:{}};return t[i].call(o.exports,o,o.exports,n),o.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{"use strict";var t={};n.r(t),n.d(t,{afterMain:()=>E,afterRead:()=>y,afterWrite:()=>x,applyStyles:()=>N,arrow:()=>tt,auto:()=>l,basePlacements:()=>c,beforeMain:()=>w,beforeRead:()=>b,beforeWrite:()=>C,bottom:()=>o,clippingParents:()=>d,computeStyles:()=>st,createPopper:()=>Nt,createPopperBase:()=>It,createPopperLite:()=>Pt,detectOverflow:()=>yt,end:()=>h,eventListeners:()=>rt,flip:()=>wt,hide:()=>Ct,left:()=>a,main:()=>A,modifierPhases:()=>O,offset:()=>Tt,placements:()=>_,popper:()=>p,popperGenerator:()=>Dt,popperOffsets:()=>xt,preventOverflow:()=>Ot,read:()=>v,reference:()=>m,right:()=>r,start:()=>u,top:()=>s,variationPlacements:()=>g,viewport:()=>f,write:()=>T});var e={};n.r(e),n.d(e,{Alert:()=>Se,Button:()=>$e,Carousel:()=>dn,Collapse:()=>xn,Dropdown:()=>Gn,Modal:()=>Di,Offcanvas:()=>Yi,Popover:()=>ws,ScrollSpy:()=>Ds,Tab:()=>eo,Toast:()=>_o,Tooltip:()=>gs});var i={};n.r(i),n.d(i,{getColor:()=>yo,hexToRgba:()=>vo,prefix:()=>bo});n(8528);var s="top",o="bottom",r="right",a="left",l="auto",c=[s,o,r,a],u="start",h="end",d="clippingParents",f="viewport",p="popper",m="reference",g=c.reduce((function(t,e){return t.concat([e+"-"+u,e+"-"+h])}),[]),_=[].concat(c,[l]).reduce((function(t,e){return t.concat([e,e+"-"+u,e+"-"+h])}),[]),b="beforeRead",v="read",y="afterRead",w="beforeMain",A="main",E="afterMain",C="beforeWrite",T="write",x="afterWrite",O=[b,v,y,w,A,E,C,T,x];function k(t){return t?(t.nodeName||"").toLowerCase():null}function S(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function L(t){return t instanceof S(t).Element||t instanceof Element}function D(t){return t instanceof S(t).HTMLElement||t instanceof HTMLElement}function I(t){return"undefined"!=typeof ShadowRoot&&(t instanceof S(t).ShadowRoot||t instanceof ShadowRoot)}const N={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var n=e.styles[t]||{},i=e.attributes[t]||{},s=e.elements[t];D(s)&&k(s)&&(Object.assign(s.style,n),Object.keys(i).forEach((function(t){var e=i[t];!1===e?s.removeAttribute(t):s.setAttribute(t,!0===e?"":e)})))}))},effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow),function(){Object.keys(e.elements).forEach((function(t){var i=e.elements[t],s=e.attributes[t]||{},o=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:n[t]).reduce((function(t,e){return t[e]="",t}),{});D(i)&&k(i)&&(Object.assign(i.style,o),Object.keys(s).forEach((function(t){i.removeAttribute(t)})))}))}},requires:["computeStyles"]};function P(t){return t.split("-")[0]}var M=Math.max,j=Math.min,F=Math.round;function H(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map((function(t){return t.brand+"/"+t.version})).join(" "):navigator.userAgent}function W(){return!/^((?!chrome|android).)*safari/i.test(H())}function z(t,e,n){void 0===e&&(e=!1),void 0===n&&(n=!1);var i=t.getBoundingClientRect(),s=1,o=1;e&&D(t)&&(s=t.offsetWidth>0&&F(i.width)/t.offsetWidth||1,o=t.offsetHeight>0&&F(i.height)/t.offsetHeight||1);var r=(L(t)?S(t):window).visualViewport,a=!W()&&n,l=(i.left+(a&&r?r.offsetLeft:0))/s,c=(i.top+(a&&r?r.offsetTop:0))/o,u=i.width/s,h=i.height/o;return{width:u,height:h,top:c,right:l+u,bottom:c+h,left:l,x:l,y:c}}function B(t){var e=z(t),n=t.offsetWidth,i=t.offsetHeight;return Math.abs(e.width-n)<=1&&(n=e.width),Math.abs(e.height-i)<=1&&(i=e.height),{x:t.offsetLeft,y:t.offsetTop,width:n,height:i}}function q(t,e){var n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&I(n)){var i=e;do{if(i&&t.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function R(t){return S(t).getComputedStyle(t)}function V(t){return["table","td","th"].indexOf(k(t))>=0}function U(t){return((L(t)?t.ownerDocument:t.document)||window.document).documentElement}function K(t){return"html"===k(t)?t:t.assignedSlot||t.parentNode||(I(t)?t.host:null)||U(t)}function Q(t){return D(t)&&"fixed"!==R(t).position?t.offsetParent:null}function X(t){for(var e=S(t),n=Q(t);n&&V(n)&&"static"===R(n).position;)n=Q(n);return n&&("html"===k(n)||"body"===k(n)&&"static"===R(n).position)?e:n||function(t){var e=/firefox/i.test(H());if(/Trident/i.test(H())&&D(t)&&"fixed"===R(t).position)return null;var n=K(t);for(I(n)&&(n=n.host);D(n)&&["html","body"].indexOf(k(n))<0;){var i=R(n);if("none"!==i.transform||"none"!==i.perspective||"paint"===i.contain||-1!==["transform","perspective"].indexOf(i.willChange)||e&&"filter"===i.willChange||e&&i.filter&&"none"!==i.filter)return n;n=n.parentNode}return null}(t)||e}function Y(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function G(t,e,n){return M(t,j(e,n))}function J(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function Z(t,e){return e.reduce((function(e,n){return e[n]=t,e}),{})}const tt={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,n=t.state,i=t.name,l=t.options,u=n.elements.arrow,h=n.modifiersData.popperOffsets,d=P(n.placement),f=Y(d),p=[a,r].indexOf(d)>=0?"height":"width";if(u&&h){var m=function(t,e){return J("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:Z(t,c))}(l.padding,n),g=B(u),_="y"===f?s:a,b="y"===f?o:r,v=n.rects.reference[p]+n.rects.reference[f]-h[f]-n.rects.popper[p],y=h[f]-n.rects.reference[f],w=X(u),A=w?"y"===f?w.clientHeight||0:w.clientWidth||0:0,E=v/2-y/2,C=m[_],T=A-g[p]-m[b],x=A/2-g[p]/2+E,O=G(C,x,T),k=f;n.modifiersData[i]=((e={})[k]=O,e.centerOffset=O-x,e)}},effect:function(t){var e=t.state,n=t.options.element,i=void 0===n?"[data-popper-arrow]":n;null!=i&&("string"!=typeof i||(i=e.elements.popper.querySelector(i)))&&q(e.elements.popper,i)&&(e.elements.arrow=i)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function et(t){return t.split("-")[1]}var nt={top:"auto",right:"auto",bottom:"auto",left:"auto"};function it(t){var e,n=t.popper,i=t.popperRect,l=t.placement,c=t.variation,u=t.offsets,d=t.position,f=t.gpuAcceleration,p=t.adaptive,m=t.roundOffsets,g=t.isFixed,_=u.x,b=void 0===_?0:_,v=u.y,y=void 0===v?0:v,w="function"==typeof m?m({x:b,y}):{x:b,y};b=w.x,y=w.y;var A=u.hasOwnProperty("x"),E=u.hasOwnProperty("y"),C=a,T=s,x=window;if(p){var O=X(n),k="clientHeight",L="clientWidth";if(O===S(n)&&"static"!==R(O=U(n)).position&&"absolute"===d&&(k="scrollHeight",L="scrollWidth"),l===s||(l===a||l===r)&&c===h)T=o,y-=(g&&O===x&&x.visualViewport?x.visualViewport.height:O[k])-i.height,y*=f?1:-1;if(l===a||(l===s||l===o)&&c===h)C=r,b-=(g&&O===x&&x.visualViewport?x.visualViewport.width:O[L])-i.width,b*=f?1:-1}var $,D=Object.assign({position:d},p&&nt),I=!0===m?function(t,e){var n=t.x,i=t.y,s=e.devicePixelRatio||1;return{x:F(n*s)/s||0,y:F(i*s)/s||0}}({x:b,y},S(n)):{x:b,y};return b=I.x,y=I.y,f?Object.assign({},D,(($={})[T]=E?"0":"",$[C]=A?"0":"",$.transform=(x.devicePixelRatio||1)<=1?"translate("+b+"px, "+y+"px)":"translate3d("+b+"px, "+y+"px, 0)",$)):Object.assign({},D,((e={})[T]=E?y+"px":"",e[C]=A?b+"px":"",e.transform="",e))}const st={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,n=t.options,i=n.gpuAcceleration,s=void 0===i||i,o=n.adaptive,r=void 0===o||o,a=n.roundOffsets,l=void 0===a||a,c={placement:P(e.placement),variation:et(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:s,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,it(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:r,roundOffsets:l})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,it(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}};var ot={passive:!0};const rt={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,n=t.instance,i=t.options,s=i.scroll,o=void 0===s||s,r=i.resize,a=void 0===r||r,l=S(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach((function(t){t.addEventListener("scroll",n.update,ot)})),a&&l.addEventListener("resize",n.update,ot),function(){o&&c.forEach((function(t){t.removeEventListener("scroll",n.update,ot)})),a&&l.removeEventListener("resize",n.update,ot)}},data:{}};var at={left:"right",right:"left",bottom:"top",top:"bottom"};function lt(t){return t.replace(/left|right|bottom|top/g,(function(t){return at[t]}))}var ct={start:"end",end:"start"};function ut(t){return t.replace(/start|end/g,(function(t){return ct[t]}))}function ht(t){var e=S(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function dt(t){return z(U(t)).left+ht(t).scrollLeft}function ft(t){var e=R(t),n=e.overflow,i=e.overflowX,s=e.overflowY;return/auto|scroll|overlay|hidden/.test(n+s+i)}function pt(t){return["html","body","#document"].indexOf(k(t))>=0?t.ownerDocument.body:D(t)&&ft(t)?t:pt(K(t))}function mt(t,e){var n;void 0===e&&(e=[]);var i=pt(t),s=i===(null==(n=t.ownerDocument)?void 0:n.body),o=S(i),r=s?[o].concat(o.visualViewport||[],ft(i)?i:[]):i,a=e.concat(r);return s?a:a.concat(mt(K(r)))}function gt(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function _t(t,e,n){return e===f?gt(function(t,e){var n=S(t),i=U(t),s=n.visualViewport,o=i.clientWidth,r=i.clientHeight,a=0,l=0;if(s){o=s.width,r=s.height;var c=W();(c||!c&&"fixed"===e)&&(a=s.offsetLeft,l=s.offsetTop)}return{width:o,height:r,x:a+dt(t),y:l}}(t,n)):L(e)?function(t,e){var n=z(t,!1,"fixed"===e);return n.top=n.top+t.clientTop,n.left=n.left+t.clientLeft,n.bottom=n.top+t.clientHeight,n.right=n.left+t.clientWidth,n.width=t.clientWidth,n.height=t.clientHeight,n.x=n.left,n.y=n.top,n}(e,n):gt(function(t){var e,n=U(t),i=ht(t),s=null==(e=t.ownerDocument)?void 0:e.body,o=M(n.scrollWidth,n.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),r=M(n.scrollHeight,n.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),a=-i.scrollLeft+dt(t),l=-i.scrollTop;return"rtl"===R(s||n).direction&&(a+=M(n.clientWidth,s?s.clientWidth:0)-o),{width:o,height:r,x:a,y:l}}(U(t)))}function bt(t,e,n,i){var s="clippingParents"===e?function(t){var e=mt(K(t)),n=["absolute","fixed"].indexOf(R(t).position)>=0&&D(t)?X(t):t;return L(n)?e.filter((function(t){return L(t)&&q(t,n)&&"body"!==k(t)})):[]}(t):[].concat(e),o=[].concat(s,[n]),r=o[0],a=o.reduce((function(e,n){var s=_t(t,n,i);return e.top=M(s.top,e.top),e.right=j(s.right,e.right),e.bottom=j(s.bottom,e.bottom),e.left=M(s.left,e.left),e}),_t(t,r,i));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function vt(t){var e,n=t.reference,i=t.element,l=t.placement,c=l?P(l):null,d=l?et(l):null,f=n.x+n.width/2-i.width/2,p=n.y+n.height/2-i.height/2;switch(c){case s:e={x:f,y:n.y-i.height};break;case o:e={x:f,y:n.y+n.height};break;case r:e={x:n.x+n.width,y:p};break;case a:e={x:n.x-i.width,y:p};break;default:e={x:n.x,y:n.y}}var m=c?Y(c):null;if(null!=m){var g="y"===m?"height":"width";switch(d){case u:e[m]=e[m]-(n[g]/2-i[g]/2);break;case h:e[m]=e[m]+(n[g]/2-i[g]/2)}}return e}function yt(t,e){void 0===e&&(e={});var n=e,i=n.placement,a=void 0===i?t.placement:i,l=n.strategy,u=void 0===l?t.strategy:l,h=n.boundary,g=void 0===h?d:h,_=n.rootBoundary,b=void 0===_?f:_,v=n.elementContext,y=void 0===v?p:v,w=n.altBoundary,A=void 0!==w&&w,E=n.padding,C=void 0===E?0:E,T=J("number"!=typeof C?C:Z(C,c)),x=y===p?m:p,O=t.rects.popper,k=t.elements[A?x:y],S=bt(L(k)?k:k.contextElement||U(t.elements.popper),g,b,u),$=z(t.elements.reference),D=vt({reference:$,element:O,strategy:"absolute",placement:a}),I=gt(Object.assign({},O,D)),N=y===p?I:$,P={top:S.top-N.top+T.top,bottom:N.bottom-S.bottom+T.bottom,left:S.left-N.left+T.left,right:N.right-S.right+T.right},M=t.modifiersData.offset;if(y===p&&M){var j=M[a];Object.keys(P).forEach((function(t){var e=[r,o].indexOf(t)>=0?1:-1,n=[s,o].indexOf(t)>=0?"y":"x";P[t]+=j[n]*e}))}return P}const wt={name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,i=t.name;if(!e.modifiersData[i]._skip){for(var h=n.mainAxis,d=void 0===h||h,f=n.altAxis,p=void 0===f||f,m=n.fallbackPlacements,b=n.padding,v=n.boundary,y=n.rootBoundary,w=n.altBoundary,A=n.flipVariations,E=void 0===A||A,C=n.allowedAutoPlacements,T=e.options.placement,x=P(T),O=m||(x===T||!E?[lt(T)]:function(t){if(P(t)===l)return[];var e=lt(t);return[ut(t),e,ut(e)]}(T)),k=[T].concat(O).reduce((function(t,n){return t.concat(P(n)===l?function(t,e){void 0===e&&(e={});var n=e,i=n.placement,s=n.boundary,o=n.rootBoundary,r=n.padding,a=n.flipVariations,l=n.allowedAutoPlacements,u=void 0===l?_:l,h=et(i),d=h?a?g:g.filter((function(t){return et(t)===h})):c,f=d.filter((function(t){return u.indexOf(t)>=0}));0===f.length&&(f=d);var p=f.reduce((function(e,n){return e[n]=yt(t,{placement:n,boundary:s,rootBoundary:o,padding:r})[P(n)],e}),{});return Object.keys(p).sort((function(t,e){return p[t]-p[e]}))}(e,{placement:n,boundary:v,rootBoundary:y,padding:b,flipVariations:E,allowedAutoPlacements:C}):n)}),[]),S=e.rects.reference,L=e.rects.popper,$=new Map,D=!0,I=k[0],N=0;N<k.length;N++){var M=k[N],j=P(M),F=et(M)===u,H=[s,o].indexOf(j)>=0,W=H?"width":"height",z=yt(e,{placement:M,boundary:v,rootBoundary:y,altBoundary:w,padding:b}),B=H?F?r:a:F?o:s;S[W]>L[W]&&(B=lt(B));var q=lt(B),R=[];if(d&&R.push(z[j]<=0),p&&R.push(z[B]<=0,z[q]<=0),R.every((function(t){return t}))){I=M,D=!1;break}$.set(M,R)}if(D)for(var V=function(t){var e=k.find((function(e){var n=$.get(e);if(n)return n.slice(0,t).every((function(t){return t}))}));if(e)return I=e,"break"},U=E?3:1;U>0;U--){if("break"===V(U))break}e.placement!==I&&(e.modifiersData[i]._skip=!0,e.placement=I,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function At(t,e,n){return void 0===n&&(n={x:0,y:0}),{top:t.top-e.height-n.y,right:t.right-e.width+n.x,bottom:t.bottom-e.height+n.y,left:t.left-e.width-n.x}}function Et(t){return[s,r,o,a].some((function(e){return t[e]>=0}))}const Ct={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,n=t.name,i=e.rects.reference,s=e.rects.popper,o=e.modifiersData.preventOverflow,r=yt(e,{elementContext:"reference"}),a=yt(e,{altBoundary:!0}),l=At(r,i),c=At(a,s,o),u=Et(l),h=Et(c);e.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:h},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":h})}};const Tt={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,n=t.options,i=t.name,o=n.offset,l=void 0===o?[0,0]:o,c=_.reduce((function(t,n){return t[n]=function(t,e,n){var i=P(t),o=[a,s].indexOf(i)>=0?-1:1,l="function"==typeof n?n(Object.assign({},e,{placement:t})):n,c=l[0],u=l[1];return c=c||0,u=(u||0)*o,[a,r].indexOf(i)>=0?{x:u,y:c}:{x:c,y:u}}(n,e.rects,l),t}),{}),u=c[e.placement],h=u.x,d=u.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=h,e.modifiersData.popperOffsets.y+=d),e.modifiersData[i]=c}};const xt={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,n=t.name;e.modifiersData[n]=vt({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}};const Ot={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,i=t.name,l=n.mainAxis,c=void 0===l||l,h=n.altAxis,d=void 0!==h&&h,f=n.boundary,p=n.rootBoundary,m=n.altBoundary,g=n.padding,_=n.tether,b=void 0===_||_,v=n.tetherOffset,y=void 0===v?0:v,w=yt(e,{boundary:f,rootBoundary:p,padding:g,altBoundary:m}),A=P(e.placement),E=et(e.placement),C=!E,T=Y(A),x="x"===T?"y":"x",O=e.modifiersData.popperOffsets,k=e.rects.reference,S=e.rects.popper,L="function"==typeof y?y(Object.assign({},e.rects,{placement:e.placement})):y,$="number"==typeof L?{mainAxis:L,altAxis:L}:Object.assign({mainAxis:0,altAxis:0},L),D=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,I={x:0,y:0};if(O){if(c){var N,F="y"===T?s:a,H="y"===T?o:r,W="y"===T?"height":"width",z=O[T],q=z+w[F],R=z-w[H],V=b?-S[W]/2:0,U=E===u?k[W]:S[W],K=E===u?-S[W]:-k[W],Q=e.elements.arrow,J=b&&Q?B(Q):{width:0,height:0},Z=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},tt=Z[F],nt=Z[H],it=G(0,k[W],J[W]),st=C?k[W]/2-V-it-tt-$.mainAxis:U-it-tt-$.mainAxis,ot=C?-k[W]/2+V+it+nt+$.mainAxis:K+it+nt+$.mainAxis,rt=e.elements.arrow&&X(e.elements.arrow),at=rt?"y"===T?rt.clientTop||0:rt.clientLeft||0:0,lt=null!=(N=null==D?void 0:D[T])?N:0,ct=z+ot-lt,ut=G(b?j(q,z+st-lt-at):q,z,b?M(R,ct):R);O[T]=ut,I[T]=ut-z}if(d){var ht,dt="x"===T?s:a,ft="x"===T?o:r,pt=O[x],mt="y"===x?"height":"width",gt=pt+w[dt],_t=pt-w[ft],bt=-1!==[s,a].indexOf(A),vt=null!=(ht=null==D?void 0:D[x])?ht:0,wt=bt?gt:pt-k[mt]-S[mt]-vt+$.altAxis,At=bt?pt+k[mt]+S[mt]-vt-$.altAxis:_t,Et=b&&bt?function(t,e,n){var i=G(t,e,n);return i>n?n:i}(wt,pt,At):G(b?wt:gt,pt,b?At:_t);O[x]=Et,I[x]=Et-pt}e.modifiersData[i]=I}},requiresIfExists:["offset"]};function kt(t,e,n){void 0===n&&(n=!1);var i,s,o=D(e),r=D(e)&&function(t){var e=t.getBoundingClientRect(),n=F(e.width)/t.offsetWidth||1,i=F(e.height)/t.offsetHeight||1;return 1!==n||1!==i}(e),a=U(e),l=z(t,r,n),c={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(o||!o&&!n)&&(("body"!==k(e)||ft(a))&&(c=(i=e)!==S(i)&&D(i)?{scrollLeft:(s=i).scrollLeft,scrollTop:s.scrollTop}:ht(i)),D(e)?((u=z(e,!0)).x+=e.clientLeft,u.y+=e.clientTop):a&&(u.x=dt(a))),{x:l.left+c.scrollLeft-u.x,y:l.top+c.scrollTop-u.y,width:l.width,height:l.height}}function St(t){var e=new Map,n=new Set,i=[];function s(t){n.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!n.has(t)){var i=e.get(t);i&&s(i)}})),i.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){n.has(t.name)||s(t)})),i}var Lt={placement:"bottom",modifiers:[],strategy:"absolute"};function $t(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return!e.some((function(t){return!(t&&"function"==typeof t.getBoundingClientRect)}))}function Dt(t){void 0===t&&(t={});var e=t,n=e.defaultModifiers,i=void 0===n?[]:n,s=e.defaultOptions,o=void 0===s?Lt:s;return function(t,e,n){void 0===n&&(n=o);var s,r,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},Lt,o),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},l=[],c=!1,u={state:a,setOptions:function(n){var s="function"==typeof n?n(a.options):n;h(),a.options=Object.assign({},o,a.options,s),a.scrollParents={reference:L(t)?mt(t):t.contextElement?mt(t.contextElement):[],popper:mt(e)};var r,c,d=function(t){var e=St(t);return O.reduce((function(t,n){return t.concat(e.filter((function(t){return t.phase===n})))}),[])}((r=[].concat(i,a.options.modifiers),c=r.reduce((function(t,e){var n=t[e.name];return t[e.name]=n?Object.assign({},n,e,{options:Object.assign({},n.options,e.options),data:Object.assign({},n.data,e.data)}):e,t}),{}),Object.keys(c).map((function(t){return c[t]}))));return a.orderedModifiers=d.filter((function(t){return t.enabled})),a.orderedModifiers.forEach((function(t){var e=t.name,n=t.options,i=void 0===n?{}:n,s=t.effect;if("function"==typeof s){var o=s({state:a,name:e,instance:u,options:i}),r=function(){};l.push(o||r)}})),u.update()},forceUpdate:function(){if(!c){var t=a.elements,e=t.reference,n=t.popper;if($t(e,n)){a.rects={reference:kt(e,X(n),"fixed"===a.options.strategy),popper:B(n)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(t){return a.modifiersData[t.name]=Object.assign({},t.data)}));for(var i=0;i<a.orderedModifiers.length;i++)if(!0!==a.reset){var s=a.orderedModifiers[i],o=s.fn,r=s.options,l=void 0===r?{}:r,h=s.name;"function"==typeof o&&(a=o({state:a,options:l,name:h,instance:u})||a)}else a.reset=!1,i=-1}}},update:(s=function(){return new Promise((function(t){u.forceUpdate(),t(a)}))},function(){return r||(r=new Promise((function(t){Promise.resolve().then((function(){r=void 0,t(s())}))}))),r}),destroy:function(){h(),c=!0}};if(!$t(t,e))return u;function h(){l.forEach((function(t){return t()})),l=[]}return u.setOptions(n).then((function(t){!c&&n.onFirstUpdate&&n.onFirstUpdate(t)})),u}}var It=Dt(),Nt=Dt({defaultModifiers:[rt,xt,st,N,Tt,wt,Ot,tt,Ct]}),Pt=Dt({defaultModifiers:[rt,xt,st,N]});
/*!
  * Bootstrap v5.3.6 (https://getbootstrap.com/)
  * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */
const Mt=new Map,jt={set(t,e,n){Mt.has(t)||Mt.set(t,new Map);const i=Mt.get(t);i.has(e)||0===i.size?i.set(e,n):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(i.keys())[0]}.`)},get:(t,e)=>Mt.has(t)&&Mt.get(t).get(e)||null,remove(t,e){if(!Mt.has(t))return;const n=Mt.get(t);n.delete(e),0===n.size&&Mt.delete(t)}},Ft="transitionend",Ht=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\s"#']+)/g,((t,e)=>`#${CSS.escape(e)}`))),t),Wt=t=>{t.dispatchEvent(new Event(Ft))},zt=t=>!(!t||"object"!=typeof t)&&(void 0!==t.jquery&&(t=t[0]),void 0!==t.nodeType),Bt=t=>zt(t)?t.jquery?t[0]:t:"string"==typeof t&&t.length>0?document.querySelector(Ht(t)):null,qt=t=>{if(!zt(t)||0===t.getClientRects().length)return!1;const e="visible"===getComputedStyle(t).getPropertyValue("visibility"),n=t.closest("details:not([open])");if(!n)return e;if(n!==t){const e=t.closest("summary");if(e&&e.parentNode!==n)return!1;if(null===e)return!1}return e},Rt=t=>!t||t.nodeType!==Node.ELEMENT_NODE||(!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled"))),Vt=t=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?Vt(t.parentNode):null},Ut=()=>{},Kt=t=>{t.offsetHeight},Qt=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,Xt=[],Yt=()=>"rtl"===document.documentElement.dir,Gt=t=>{var e;e=()=>{const e=Qt();if(e){const n=t.NAME,i=e.fn[n];e.fn[n]=t.jQueryInterface,e.fn[n].Constructor=t,e.fn[n].noConflict=()=>(e.fn[n]=i,t.jQueryInterface)}},"loading"===document.readyState?(Xt.length||document.addEventListener("DOMContentLoaded",(()=>{for(const t of Xt)t()})),Xt.push(e)):e()},Jt=(t,e=[],n=t)=>"function"==typeof t?t.call(...e):n,Zt=(t,e,n=!0)=>{if(!n)return void Jt(t);const i=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:n}=window.getComputedStyle(t);const i=Number.parseFloat(e),s=Number.parseFloat(n);return i||s?(e=e.split(",")[0],n=n.split(",")[0],1e3*(Number.parseFloat(e)+Number.parseFloat(n))):0})(e)+5;let s=!1;const o=({target:n})=>{n===e&&(s=!0,e.removeEventListener(Ft,o),Jt(t))};e.addEventListener(Ft,o),setTimeout((()=>{s||Wt(e)}),i)},te=(t,e,n,i)=>{const s=t.length;let o=t.indexOf(e);return-1===o?!n&&i?t[s-1]:t[0]:(o+=n?1:-1,i&&(o=(o+s)%s),t[Math.max(0,Math.min(o,s-1))])},ee=/[^.]*(?=\..*)\.|.*/,ne=/\..*/,ie=/::\d+$/,se={};let oe=1;const re={mouseenter:"mouseover",mouseleave:"mouseout"},ae=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function le(t,e){return e&&`${e}::${oe++}`||t.uidEvent||oe++}function ce(t){const e=le(t);return t.uidEvent=e,se[e]=se[e]||{},se[e]}function ue(t,e,n=null){return Object.values(t).find((t=>t.callable===e&&t.delegationSelector===n))}function he(t,e,n){const i="string"==typeof e,s=i?n:e||n;let o=me(t);return ae.has(o)||(o=t),[i,s,o]}function de(t,e,n,i,s){if("string"!=typeof e||!t)return;let[o,r,a]=he(e,n,i);if(e in re){const t=t=>function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)};r=t(r)}const l=ce(t),c=l[a]||(l[a]={}),u=ue(c,r,o?n:null);if(u)return void(u.oneOff=u.oneOff&&s);const h=le(r,e.replace(ee,"")),d=o?function(t,e,n){return function i(s){const o=t.querySelectorAll(e);for(let{target:r}=s;r&&r!==this;r=r.parentNode)for(const a of o)if(a===r)return _e(s,{delegateTarget:r}),i.oneOff&&ge.off(t,s.type,e,n),n.apply(r,[s])}}(t,n,r):function(t,e){return function n(i){return _e(i,{delegateTarget:t}),n.oneOff&&ge.off(t,i.type,e),e.apply(t,[i])}}(t,r);d.delegationSelector=o?n:null,d.callable=r,d.oneOff=s,d.uidEvent=h,c[h]=d,t.addEventListener(a,d,o)}function fe(t,e,n,i,s){const o=ue(e[n],i,s);o&&(t.removeEventListener(n,o,Boolean(s)),delete e[n][o.uidEvent])}function pe(t,e,n,i){const s=e[n]||{};for(const[o,r]of Object.entries(s))o.includes(i)&&fe(t,e,n,r.callable,r.delegationSelector)}function me(t){return t=t.replace(ne,""),re[t]||t}const ge={on(t,e,n,i){de(t,e,n,i,!1)},one(t,e,n,i){de(t,e,n,i,!0)},off(t,e,n,i){if("string"!=typeof e||!t)return;const[s,o,r]=he(e,n,i),a=r!==e,l=ce(t),c=l[r]||{},u=e.startsWith(".");if(void 0===o){if(u)for(const n of Object.keys(l))pe(t,l,n,e.slice(1));for(const[n,i]of Object.entries(c)){const s=n.replace(ie,"");a&&!e.includes(s)||fe(t,l,r,i.callable,i.delegationSelector)}}else{if(!Object.keys(c).length)return;fe(t,l,r,o,s?n:null)}},trigger(t,e,n){if("string"!=typeof e||!t)return null;const i=Qt();let s=null,o=!0,r=!0,a=!1;e!==me(e)&&i&&(s=i.Event(e,n),i(t).trigger(s),o=!s.isPropagationStopped(),r=!s.isImmediatePropagationStopped(),a=s.isDefaultPrevented());const l=_e(new Event(e,{bubbles:o,cancelable:!0}),n);return a&&l.preventDefault(),r&&t.dispatchEvent(l),l.defaultPrevented&&s&&s.preventDefault(),l}};function _e(t,e={}){for(const[n,i]of Object.entries(e))try{t[n]=i}catch(e){Object.defineProperty(t,n,{configurable:!0,get:()=>i})}return t}function be(t){if("true"===t)return!0;if("false"===t)return!1;if(t===Number(t).toString())return Number(t);if(""===t||"null"===t)return null;if("string"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function ve(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const ye={setDataAttribute(t,e,n){t.setAttribute(`data-bs-${ve(e)}`,n)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${ve(e)}`)},getDataAttributes(t){if(!t)return{};const e={},n=Object.keys(t.dataset).filter((t=>t.startsWith("bs")&&!t.startsWith("bsConfig")));for(const i of n){let n=i.replace(/^bs/,"");n=n.charAt(0).toLowerCase()+n.slice(1),e[n]=be(t.dataset[i])}return e},getDataAttribute:(t,e)=>be(t.getAttribute(`data-bs-${ve(e)}`))};class we{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const n=zt(e)?ye.getDataAttribute(e,"config"):{};return{...this.constructor.Default,..."object"==typeof n?n:{},...zt(e)?ye.getDataAttributes(e):{},..."object"==typeof t?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[i,s]of Object.entries(e)){const e=t[i],o=zt(e)?"element":null==(n=e)?`${n}`:Object.prototype.toString.call(n).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(s).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${i}" provided type "${o}" but expected type "${s}".`)}var n}}class Ae extends we{constructor(t,e){super(),(t=Bt(t))&&(this._element=t,this._config=this._getConfig(e),jt.set(this._element,this.constructor.DATA_KEY,this))}dispose(){jt.remove(this._element,this.constructor.DATA_KEY),ge.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,n=!0){Zt(t,e,n)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return jt.get(Bt(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return"5.3.6"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const Ee=t=>{let e=t.getAttribute("data-bs-target");if(!e||"#"===e){let n=t.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),e=n&&"#"!==n?n.trim():null}return e?e.split(",").map((t=>Ht(t))).join(","):null},Ce={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const n=[];let i=t.parentNode.closest(e);for(;i;)n.push(i),i=i.parentNode.closest(e);return n},prev(t,e){let n=t.previousElementSibling;for(;n;){if(n.matches(e))return[n];n=n.previousElementSibling}return[]},next(t,e){let n=t.nextElementSibling;for(;n;){if(n.matches(e))return[n];n=n.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((t=>`${t}:not([tabindex^="-"])`)).join(",");return this.find(e,t).filter((t=>!Rt(t)&&qt(t)))},getSelectorFromElement(t){const e=Ee(t);return e&&Ce.findOne(e)?e:null},getElementFromSelector(t){const e=Ee(t);return e?Ce.findOne(e):null},getMultipleElementsFromSelector(t){const e=Ee(t);return e?Ce.find(e):[]}},Te=(t,e="hide")=>{const n=`click.dismiss${t.EVENT_KEY}`,i=t.NAME;ge.on(document,n,`[data-bs-dismiss="${i}"]`,(function(n){if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),Rt(this))return;const s=Ce.getElementFromSelector(this)||this.closest(`.${i}`);t.getOrCreateInstance(s)[e]()}))},xe=".bs.alert",Oe=`close${xe}`,ke=`closed${xe}`;class Se extends Ae{static get NAME(){return"alert"}close(){if(ge.trigger(this._element,Oe).defaultPrevented)return;this._element.classList.remove("show");const t=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,t)}_destroyElement(){this._element.remove(),ge.trigger(this._element,ke),this.dispose()}static jQueryInterface(t){return this.each((function(){const e=Se.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}Te(Se,"close"),Gt(Se);const Le='[data-bs-toggle="button"]';class $e extends Ae{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(t){return this.each((function(){const e=$e.getOrCreateInstance(this);"toggle"===t&&e[t]()}))}}ge.on(document,"click.bs.button.data-api",Le,(t=>{t.preventDefault();const e=t.target.closest(Le);$e.getOrCreateInstance(e).toggle()})),Gt($e);const De=".bs.swipe",Ie=`touchstart${De}`,Ne=`touchmove${De}`,Pe=`touchend${De}`,Me=`pointerdown${De}`,je=`pointerup${De}`,Fe={endCallback:null,leftCallback:null,rightCallback:null},He={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class We extends we{constructor(t,e){super(),this._element=t,t&&We.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return Fe}static get DefaultType(){return He}static get NAME(){return"swipe"}dispose(){ge.off(this._element,De)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),Jt(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=40)return;const e=t/this._deltaX;this._deltaX=0,e&&Jt(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(ge.on(this._element,Me,(t=>this._start(t))),ge.on(this._element,je,(t=>this._end(t))),this._element.classList.add("pointer-event")):(ge.on(this._element,Ie,(t=>this._start(t))),ge.on(this._element,Ne,(t=>this._move(t))),ge.on(this._element,Pe,(t=>this._end(t))))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&("pen"===t.pointerType||"touch"===t.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const ze=".bs.carousel",Be=".data-api",qe="ArrowLeft",Re="ArrowRight",Ve="next",Ue="prev",Ke="left",Qe="right",Xe=`slide${ze}`,Ye=`slid${ze}`,Ge=`keydown${ze}`,Je=`mouseenter${ze}`,Ze=`mouseleave${ze}`,tn=`dragstart${ze}`,en=`load${ze}${Be}`,nn=`click${ze}${Be}`,sn="carousel",on="active",rn=".active",an=".carousel-item",ln=rn+an,cn={[qe]:Qe,[Re]:Ke},un={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},hn={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class dn extends Ae{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=Ce.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===sn&&this.cycle()}static get Default(){return un}static get DefaultType(){return hn}static get NAME(){return"carousel"}next(){this._slide(Ve)}nextWhenVisible(){!document.hidden&&qt(this._element)&&this.next()}prev(){this._slide(Ue)}pause(){this._isSliding&&Wt(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?ge.one(this._element,Ye,(()=>this.cycle())):this.cycle())}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding)return void ge.one(this._element,Ye,(()=>this.to(t)));const n=this._getItemIndex(this._getActive());if(n===t)return;const i=t>n?Ve:Ue;this._slide(i,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&ge.on(this._element,Ge,(t=>this._keydown(t))),"hover"===this._config.pause&&(ge.on(this._element,Je,(()=>this.pause())),ge.on(this._element,Ze,(()=>this._maybeEnableCycle()))),this._config.touch&&We.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of Ce.find(".carousel-item img",this._element))ge.on(t,tn,(t=>t.preventDefault()));const t={leftCallback:()=>this._slide(this._directionToOrder(Ke)),rightCallback:()=>this._slide(this._directionToOrder(Qe)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new We(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=cn[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=Ce.findOne(rn,this._indicatorsElement);e.classList.remove(on),e.removeAttribute("aria-current");const n=Ce.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);n&&(n.classList.add(on),n.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const n=this._getActive(),i=t===Ve,s=e||te(this._getItems(),n,i,this._config.wrap);if(s===n)return;const o=this._getItemIndex(s),r=e=>ge.trigger(this._element,e,{relatedTarget:s,direction:this._orderToDirection(t),from:this._getItemIndex(n),to:o});if(r(Xe).defaultPrevented)return;if(!n||!s)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=s;const l=i?"carousel-item-start":"carousel-item-end",c=i?"carousel-item-next":"carousel-item-prev";s.classList.add(c),Kt(s),n.classList.add(l),s.classList.add(l);this._queueCallback((()=>{s.classList.remove(l,c),s.classList.add(on),n.classList.remove(on,c,l),this._isSliding=!1,r(Ye)}),n,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return Ce.findOne(ln,this._element)}_getItems(){return Ce.find(an,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return Yt()?t===Ke?Ue:Ve:t===Ke?Ve:Ue}_orderToDirection(t){return Yt()?t===Ue?Ke:Qe:t===Ue?Qe:Ke}static jQueryInterface(t){return this.each((function(){const e=dn.getOrCreateInstance(this,t);if("number"!=typeof t){if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}else e.to(t)}))}}ge.on(document,nn,"[data-bs-slide], [data-bs-slide-to]",(function(t){const e=Ce.getElementFromSelector(this);if(!e||!e.classList.contains(sn))return;t.preventDefault();const n=dn.getOrCreateInstance(e),i=this.getAttribute("data-bs-slide-to");return i?(n.to(i),void n._maybeEnableCycle()):"next"===ye.getDataAttribute(this,"slide")?(n.next(),void n._maybeEnableCycle()):(n.prev(),void n._maybeEnableCycle())})),ge.on(window,en,(()=>{const t=Ce.find('[data-bs-ride="carousel"]');for(const e of t)dn.getOrCreateInstance(e)})),Gt(dn);const fn=".bs.collapse",pn=`show${fn}`,mn=`shown${fn}`,gn=`hide${fn}`,_n=`hidden${fn}`,bn=`click${fn}.data-api`,vn="show",yn="collapse",wn="collapsing",An=`:scope .${yn} .${yn}`,En='[data-bs-toggle="collapse"]',Cn={parent:null,toggle:!0},Tn={parent:"(null|element)",toggle:"boolean"};class xn extends Ae{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const n=Ce.find(En);for(const t of n){const e=Ce.getSelectorFromElement(t),n=Ce.find(e).filter((t=>t===this._element));null!==e&&n.length&&this._triggerArray.push(t)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Cn}static get DefaultType(){return Tn}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((t=>t!==this._element)).map((t=>xn.getOrCreateInstance(t,{toggle:!1})))),t.length&&t[0]._isTransitioning)return;if(ge.trigger(this._element,pn).defaultPrevented)return;for(const e of t)e.hide();const e=this._getDimension();this._element.classList.remove(yn),this._element.classList.add(wn),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const n=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(wn),this._element.classList.add(yn,vn),this._element.style[e]="",ge.trigger(this._element,mn)}),this._element,!0),this._element.style[e]=`${this._element[n]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(ge.trigger(this._element,gn).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,Kt(this._element),this._element.classList.add(wn),this._element.classList.remove(yn,vn);for(const t of this._triggerArray){const e=Ce.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0;this._element.style[t]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(wn),this._element.classList.add(yn),ge.trigger(this._element,_n)}),this._element,!0)}_isShown(t=this._element){return t.classList.contains(vn)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=Bt(t.parent),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(En);for(const e of t){const t=Ce.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(t){const e=Ce.find(An,this._config.parent);return Ce.find(t,this._config.parent).filter((t=>!e.includes(t)))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const n of t)n.classList.toggle("collapsed",!e),n.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return"string"==typeof t&&/show|hide/.test(t)&&(e.toggle=!1),this.each((function(){const n=xn.getOrCreateInstance(this,e);if("string"==typeof t){if(void 0===n[t])throw new TypeError(`No method named "${t}"`);n[t]()}}))}}ge.on(document,bn,En,(function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();for(const t of Ce.getMultipleElementsFromSelector(this))xn.getOrCreateInstance(t,{toggle:!1}).toggle()})),Gt(xn);const On="dropdown",kn=".bs.dropdown",Sn=".data-api",Ln="ArrowUp",$n="ArrowDown",Dn=`hide${kn}`,In=`hidden${kn}`,Nn=`show${kn}`,Pn=`shown${kn}`,Mn=`click${kn}${Sn}`,jn=`keydown${kn}${Sn}`,Fn=`keyup${kn}${Sn}`,Hn="show",Wn='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',zn=`${Wn}.${Hn}`,Bn=".dropdown-menu",qn=Yt()?"top-end":"top-start",Rn=Yt()?"top-start":"top-end",Vn=Yt()?"bottom-end":"bottom-start",Un=Yt()?"bottom-start":"bottom-end",Kn=Yt()?"left-start":"right-start",Qn=Yt()?"right-start":"left-start",Xn={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Yn={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Gn extends Ae{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=Ce.next(this._element,Bn)[0]||Ce.prev(this._element,Bn)[0]||Ce.findOne(Bn,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Xn}static get DefaultType(){return Yn}static get NAME(){return On}toggle(){return this._isShown()?this.hide():this.show()}show(){if(Rt(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!ge.trigger(this._element,Nn,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const t of[].concat(...document.body.children))ge.on(t,"mouseover",Ut);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Hn),this._element.classList.add(Hn),ge.trigger(this._element,Pn,t)}}hide(){if(Rt(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!ge.trigger(this._element,Dn,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))ge.off(t,"mouseover",Ut);this._popper&&this._popper.destroy(),this._menu.classList.remove(Hn),this._element.classList.remove(Hn),this._element.setAttribute("aria-expanded","false"),ye.removeDataAttribute(this._menu,"popper"),ge.trigger(this._element,In,t),this._element.focus()}}_getConfig(t){if("object"==typeof(t=super._getConfig(t)).reference&&!zt(t.reference)&&"function"!=typeof t.reference.getBoundingClientRect)throw new TypeError(`${On.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){let t=this._element;"parent"===this._config.reference?t=this._parent:zt(this._config.reference)?t=Bt(this._config.reference):"object"==typeof this._config.reference&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=Nt(t,this._menu,e)}_isShown(){return this._menu.classList.contains(Hn)}_getPlacement(){const t=this._parent;if(t.classList.contains("dropend"))return Kn;if(t.classList.contains("dropstart"))return Qn;if(t.classList.contains("dropup-center"))return"top";if(t.classList.contains("dropdown-center"))return"bottom";const e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return t.classList.contains("dropup")?e?Rn:qn:e?Un:Vn}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(ye.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...Jt(this._config.popperConfig,[void 0,t])}}_selectMenuItem({key:t,target:e}){const n=Ce.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((t=>qt(t)));n.length&&te(n,e,t===$n,!n.includes(e)).focus()}static jQueryInterface(t){return this.each((function(){const e=Gn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}static clearMenus(t){if(2===t.button||"keyup"===t.type&&"Tab"!==t.key)return;const e=Ce.find(zn);for(const n of e){const e=Gn.getInstance(n);if(!e||!1===e._config.autoClose)continue;const i=t.composedPath(),s=i.includes(e._menu);if(i.includes(e._element)||"inside"===e._config.autoClose&&!s||"outside"===e._config.autoClose&&s)continue;if(e._menu.contains(t.target)&&("keyup"===t.type&&"Tab"===t.key||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const o={relatedTarget:e._element};"click"===t.type&&(o.clickEvent=t),e._completeHide(o)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),n="Escape"===t.key,i=[Ln,$n].includes(t.key);if(!i&&!n)return;if(e&&!n)return;t.preventDefault();const s=this.matches(Wn)?this:Ce.prev(this,Wn)[0]||Ce.next(this,Wn)[0]||Ce.findOne(Wn,t.delegateTarget.parentNode),o=Gn.getOrCreateInstance(s);if(i)return t.stopPropagation(),o.show(),void o._selectMenuItem(t);o._isShown()&&(t.stopPropagation(),o.hide(),s.focus())}}ge.on(document,jn,Wn,Gn.dataApiKeydownHandler),ge.on(document,jn,Bn,Gn.dataApiKeydownHandler),ge.on(document,Mn,Gn.clearMenus),ge.on(document,Fn,Gn.clearMenus),ge.on(document,Mn,Wn,(function(t){t.preventDefault(),Gn.getOrCreateInstance(this).toggle()})),Gt(Gn);const Jn="backdrop",Zn="show",ti=`mousedown.bs.${Jn}`,ei={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},ni={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class ii extends we{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return ei}static get DefaultType(){return ni}static get NAME(){return Jn}show(t){if(!this._config.isVisible)return void Jt(t);this._append();const e=this._getElement();this._config.isAnimated&&Kt(e),e.classList.add(Zn),this._emulateAnimation((()=>{Jt(t)}))}hide(t){this._config.isVisible?(this._getElement().classList.remove(Zn),this._emulateAnimation((()=>{this.dispose(),Jt(t)}))):Jt(t)}dispose(){this._isAppended&&(ge.off(this._element,ti),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=Bt(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),ge.on(t,ti,(()=>{Jt(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(t){Zt(t,this._getElement(),this._config.isAnimated)}}const si=".bs.focustrap",oi=`focusin${si}`,ri=`keydown.tab${si}`,ai="backward",li={autofocus:!0,trapElement:null},ci={autofocus:"boolean",trapElement:"element"};class ui extends we{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return li}static get DefaultType(){return ci}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),ge.off(document,si),ge.on(document,oi,(t=>this._handleFocusin(t))),ge.on(document,ri,(t=>this._handleKeydown(t))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,ge.off(document,si))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const n=Ce.focusableChildren(e);0===n.length?e.focus():this._lastTabNavDirection===ai?n[n.length-1].focus():n[0].focus()}_handleKeydown(t){"Tab"===t.key&&(this._lastTabNavDirection=t.shiftKey?ai:"forward")}}const hi=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",di=".sticky-top",fi="padding-right",pi="margin-right";class mi{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,fi,(e=>e+t)),this._setElementAttributes(hi,fi,(e=>e+t)),this._setElementAttributes(di,pi,(e=>e-t))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,fi),this._resetElementAttributes(hi,fi),this._resetElementAttributes(di,pi)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,n){const i=this.getWidth();this._applyManipulationCallback(t,(t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+i)return;this._saveInitialAttribute(t,e);const s=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${n(Number.parseFloat(s))}px`)}))}_saveInitialAttribute(t,e){const n=t.style.getPropertyValue(e);n&&ye.setDataAttribute(t,e,n)}_resetElementAttributes(t,e){this._applyManipulationCallback(t,(t=>{const n=ye.getDataAttribute(t,e);null!==n?(ye.removeDataAttribute(t,e),t.style.setProperty(e,n)):t.style.removeProperty(e)}))}_applyManipulationCallback(t,e){if(zt(t))e(t);else for(const n of Ce.find(t,this._element))e(n)}}const gi=".bs.modal",_i=`hide${gi}`,bi=`hidePrevented${gi}`,vi=`hidden${gi}`,yi=`show${gi}`,wi=`shown${gi}`,Ai=`resize${gi}`,Ei=`click.dismiss${gi}`,Ci=`mousedown.dismiss${gi}`,Ti=`keydown.dismiss${gi}`,xi=`click${gi}.data-api`,Oi="modal-open",ki="show",Si="modal-static",Li={backdrop:!0,focus:!0,keyboard:!0},$i={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Di extends Ae{constructor(t,e){super(t,e),this._dialog=Ce.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new mi,this._addEventListeners()}static get Default(){return Li}static get DefaultType(){return $i}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||this._isTransitioning)return;ge.trigger(this._element,yi,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Oi),this._adjustDialog(),this._backdrop.show((()=>this._showElement(t))))}hide(){if(!this._isShown||this._isTransitioning)return;ge.trigger(this._element,_i).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(ki),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated()))}dispose(){ge.off(window,gi),ge.off(this._dialog,gi),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new ii({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new ui({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=Ce.findOne(".modal-body",this._dialog);e&&(e.scrollTop=0),Kt(this._element),this._element.classList.add(ki);this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,ge.trigger(this._element,wi,{relatedTarget:t})}),this._dialog,this._isAnimated())}_addEventListeners(){ge.on(this._element,Ti,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),ge.on(window,Ai,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),ge.on(this._element,Ci,(t=>{ge.one(this._element,Ei,(e=>{this._element===t.target&&this._element===e.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(Oi),this._resetAdjustments(),this._scrollBar.reset(),ge.trigger(this._element,vi)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(ge.trigger(this._element,bi).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;"hidden"===e||this._element.classList.contains(Si)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(Si),this._queueCallback((()=>{this._element.classList.remove(Si),this._queueCallback((()=>{this._element.style.overflowY=e}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),n=e>0;if(n&&!t){const t=Yt()?"paddingLeft":"paddingRight";this._element.style[t]=`${e}px`}if(!n&&t){const t=Yt()?"paddingRight":"paddingLeft";this._element.style[t]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each((function(){const n=Di.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===n[t])throw new TypeError(`No method named "${t}"`);n[t](e)}}))}}ge.on(document,xi,'[data-bs-toggle="modal"]',(function(t){const e=Ce.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),ge.one(e,yi,(t=>{t.defaultPrevented||ge.one(e,vi,(()=>{qt(this)&&this.focus()}))}));const n=Ce.findOne(".modal.show");n&&Di.getInstance(n).hide();Di.getOrCreateInstance(e).toggle(this)})),Te(Di),Gt(Di);const Ii=".bs.offcanvas",Ni=".data-api",Pi=`load${Ii}${Ni}`,Mi="show",ji="showing",Fi="hiding",Hi=".offcanvas.show",Wi=`show${Ii}`,zi=`shown${Ii}`,Bi=`hide${Ii}`,qi=`hidePrevented${Ii}`,Ri=`hidden${Ii}`,Vi=`resize${Ii}`,Ui=`click${Ii}${Ni}`,Ki=`keydown.dismiss${Ii}`,Qi={backdrop:!0,keyboard:!0,scroll:!1},Xi={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Yi extends Ae{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Qi}static get DefaultType(){return Xi}static get NAME(){return"offcanvas"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown)return;if(ge.trigger(this._element,Wi,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||(new mi).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(ji);this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(Mi),this._element.classList.remove(ji),ge.trigger(this._element,zi,{relatedTarget:t})}),this._element,!0)}hide(){if(!this._isShown)return;if(ge.trigger(this._element,Bi).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Fi),this._backdrop.hide();this._queueCallback((()=>{this._element.classList.remove(Mi,Fi),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new mi).reset(),ge.trigger(this._element,Ri)}),this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=Boolean(this._config.backdrop);return new ii({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{"static"!==this._config.backdrop?this.hide():ge.trigger(this._element,qi)}:null})}_initializeFocusTrap(){return new ui({trapElement:this._element})}_addEventListeners(){ge.on(this._element,Ki,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():ge.trigger(this._element,qi))}))}static jQueryInterface(t){return this.each((function(){const e=Yi.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}ge.on(document,Ui,'[data-bs-toggle="offcanvas"]',(function(t){const e=Ce.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),Rt(this))return;ge.one(e,Ri,(()=>{qt(this)&&this.focus()}));const n=Ce.findOne(Hi);n&&n!==e&&Yi.getInstance(n).hide();Yi.getOrCreateInstance(e).toggle(this)})),ge.on(window,Pi,(()=>{for(const t of Ce.find(Hi))Yi.getOrCreateInstance(t).show()})),ge.on(window,Vi,(()=>{for(const t of Ce.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(t).position&&Yi.getOrCreateInstance(t).hide()})),Te(Yi),Gt(Yi);const Gi={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Ji=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Zi=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,ts=(t,e)=>{const n=t.nodeName.toLowerCase();return e.includes(n)?!Ji.has(n)||Boolean(Zi.test(t.nodeValue)):e.filter((t=>t instanceof RegExp)).some((t=>t.test(n)))};const es={allowList:Gi,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},ns={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},is={entry:"(string|element|function|null)",selector:"(string|element)"};class ss extends we{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return es}static get DefaultType(){return ns}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map((t=>this._resolvePossibleFunction(t))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[e,n]of Object.entries(this._config.content))this._setContent(t,n,e);const e=t.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&e.classList.add(...n.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,n]of Object.entries(t))super._typeCheckConfig({selector:e,entry:n},is)}_setContent(t,e,n){const i=Ce.findOne(n,t);i&&((e=this._resolvePossibleFunction(e))?zt(e)?this._putElementInTemplate(Bt(e),i):this._config.html?i.innerHTML=this._maybeSanitize(e):i.textContent=e:i.remove())}_maybeSanitize(t){return this._config.sanitize?function(t,e,n){if(!t.length)return t;if(n&&"function"==typeof n)return n(t);const i=(new window.DOMParser).parseFromString(t,"text/html"),s=[].concat(...i.body.querySelectorAll("*"));for(const t of s){const n=t.nodeName.toLowerCase();if(!Object.keys(e).includes(n)){t.remove();continue}const i=[].concat(...t.attributes),s=[].concat(e["*"]||[],e[n]||[]);for(const e of i)ts(e,s)||t.removeAttribute(e.nodeName)}return i.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return Jt(t,[void 0,this])}_putElementInTemplate(t,e){if(this._config.html)return e.innerHTML="",void e.append(t);e.textContent=t.textContent}}const os=new Set(["sanitize","allowList","sanitizeFn"]),rs="fade",as="show",ls=".tooltip-inner",cs=".modal",us="hide.bs.modal",hs="hover",ds="focus",fs={AUTO:"auto",TOP:"top",RIGHT:Yt()?"left":"right",BOTTOM:"bottom",LEFT:Yt()?"right":"left"},ps={allowList:Gi,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},ms={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class gs extends Ae{constructor(t,e){super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return ps}static get DefaultType(){return ms}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),ge.off(this._element.closest(cs),us,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=ge.trigger(this._element,this.constructor.eventName("show")),e=(Vt(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();const n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));const{container:i}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(i.append(n),ge.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(n),n.classList.add(as),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))ge.on(t,"mouseover",Ut);this._queueCallback((()=>{ge.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(ge.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented)return;if(this._getTipElement().classList.remove(as),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))ge.off(t,"mouseover",Ut);this._activeTrigger.click=!1,this._activeTrigger[ds]=!1,this._activeTrigger[hs]=!1,this._isHovered=null;this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),ge.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(rs,as),e.classList.add(`bs-${this.constructor.NAME}-auto`);const n=(t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t})(this.constructor.NAME).toString();return e.setAttribute("id",n),this._isAnimated()&&e.classList.add(rs),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new ss({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[ls]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(rs)}_isShown(){return this.tip&&this.tip.classList.contains(as)}_createPopper(t){const e=Jt(this._config.placement,[this,t,this._element]),n=fs[e.toUpperCase()];return Nt(this._element,t,this._getPopperConfig(n))}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return Jt(t,[this._element,this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...e,...Jt(this._config.popperConfig,[void 0,e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if("click"===e)ge.on(this._element,this.constructor.eventName("click"),this._config.selector,(t=>{this._initializeOnDelegatedTarget(t).toggle()}));else if("manual"!==e){const t=e===hs?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),n=e===hs?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");ge.on(this._element,t,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?ds:hs]=!0,e._enter()})),ge.on(this._element,n,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?ds:hs]=e._element.contains(t.relatedTarget),e._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},ge.on(this._element.closest(cs),us,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=ye.getDataAttributes(this._element);for(const t of Object.keys(e))os.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:Bt(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,n]of Object.entries(this._config))this.constructor.Default[e]!==n&&(t[e]=n);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each((function(){const e=gs.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}Gt(gs);const _s=".popover-header",bs=".popover-body",vs={...gs.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},ys={...gs.DefaultType,content:"(null|string|element|function)"};class ws extends gs{static get Default(){return vs}static get DefaultType(){return ys}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[_s]:this._getTitle(),[bs]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each((function(){const e=ws.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}Gt(ws);const As=".bs.scrollspy",Es=`activate${As}`,Cs=`click${As}`,Ts=`load${As}.data-api`,xs="active",Os="[href]",ks=".nav-link",Ss=`${ks}, .nav-item > ${ks}, .list-group-item`,Ls={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},$s={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Ds extends Ae{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Ls}static get DefaultType(){return $s}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=Bt(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,"string"==typeof t.threshold&&(t.threshold=t.threshold.split(",").map((t=>Number.parseFloat(t)))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(ge.off(this._config.target,Cs),ge.on(this._config.target,Cs,Os,(t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const n=this._rootElement||window,i=e.offsetTop-this._element.offsetTop;if(n.scrollTo)return void n.scrollTo({top:i,behavior:"smooth"});n.scrollTop=i}})))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((t=>this._observerCallback(t)),t)}_observerCallback(t){const e=t=>this._targetLinks.get(`#${t.target.id}`),n=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},i=(this._rootElement||document.documentElement).scrollTop,s=i>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=i;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const t=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(s&&t){if(n(o),!i)return}else s||t||n(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=Ce.find(Os,this._config.target);for(const e of t){if(!e.hash||Rt(e))continue;const t=Ce.findOne(decodeURI(e.hash),this._element);qt(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(xs),this._activateParents(t),ge.trigger(this._element,Es,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains("dropdown-item"))Ce.findOne(".dropdown-toggle",t.closest(".dropdown")).classList.add(xs);else for(const e of Ce.parents(t,".nav, .list-group"))for(const t of Ce.prev(e,Ss))t.classList.add(xs)}_clearActiveClass(t){t.classList.remove(xs);const e=Ce.find(`${Os}.${xs}`,t);for(const t of e)t.classList.remove(xs)}static jQueryInterface(t){return this.each((function(){const e=Ds.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}ge.on(window,Ts,(()=>{for(const t of Ce.find('[data-bs-spy="scroll"]'))Ds.getOrCreateInstance(t)})),Gt(Ds);const Is=".bs.tab",Ns=`hide${Is}`,Ps=`hidden${Is}`,Ms=`show${Is}`,js=`shown${Is}`,Fs=`click${Is}`,Hs=`keydown${Is}`,Ws=`load${Is}`,zs="ArrowLeft",Bs="ArrowRight",qs="ArrowUp",Rs="ArrowDown",Vs="Home",Us="End",Ks="active",Qs="fade",Xs="show",Ys=".dropdown-toggle",Gs=`:not(${Ys})`,Js='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Zs=`${`.nav-link${Gs}, .list-group-item${Gs}, [role="tab"]${Gs}`}, ${Js}`,to=`.${Ks}[data-bs-toggle="tab"], .${Ks}[data-bs-toggle="pill"], .${Ks}[data-bs-toggle="list"]`;class eo extends Ae{constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),ge.on(this._element,Hs,(t=>this._keydown(t))))}static get NAME(){return"tab"}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),n=e?ge.trigger(e,Ns,{relatedTarget:t}):null;ge.trigger(t,Ms,{relatedTarget:e}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){if(!t)return;t.classList.add(Ks),this._activate(Ce.getElementFromSelector(t));this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),ge.trigger(t,js,{relatedTarget:e})):t.classList.add(Xs)}),t,t.classList.contains(Qs))}_deactivate(t,e){if(!t)return;t.classList.remove(Ks),t.blur(),this._deactivate(Ce.getElementFromSelector(t));this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),ge.trigger(t,Ps,{relatedTarget:e})):t.classList.remove(Xs)}),t,t.classList.contains(Qs))}_keydown(t){if(![zs,Bs,qs,Rs,Vs,Us].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter((t=>!Rt(t)));let n;if([Vs,Us].includes(t.key))n=e[t.key===Vs?0:e.length-1];else{const i=[Bs,Rs].includes(t.key);n=te(e,t.target,i,!0)}n&&(n.focus({preventScroll:!0}),eo.getOrCreateInstance(n).show())}_getChildren(){return Ce.find(Zs,this._parent)}_getActiveElem(){return this._getChildren().find((t=>this._elemIsActive(t)))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),n=this._getOuterElement(t);t.setAttribute("aria-selected",e),n!==t&&this._setAttributeIfNotExists(n,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=Ce.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const n=this._getOuterElement(t);if(!n.classList.contains("dropdown"))return;const i=(t,i)=>{const s=Ce.findOne(t,n);s&&s.classList.toggle(i,e)};i(Ys,Ks),i(".dropdown-menu",Xs),n.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,n){t.hasAttribute(e)||t.setAttribute(e,n)}_elemIsActive(t){return t.classList.contains(Ks)}_getInnerElement(t){return t.matches(Zs)?t:Ce.findOne(Zs,t)}_getOuterElement(t){return t.closest(".nav-item, .list-group-item")||t}static jQueryInterface(t){return this.each((function(){const e=eo.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}ge.on(document,Fs,Js,(function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),Rt(this)||eo.getOrCreateInstance(this).show()})),ge.on(window,Ws,(()=>{for(const t of Ce.find(to))eo.getOrCreateInstance(t)})),Gt(eo);const no=".bs.toast",io=`mouseover${no}`,so=`mouseout${no}`,oo=`focusin${no}`,ro=`focusout${no}`,ao=`hide${no}`,lo=`hidden${no}`,co=`show${no}`,uo=`shown${no}`,ho="hide",fo="show",po="showing",mo={animation:"boolean",autohide:"boolean",delay:"number"},go={animation:!0,autohide:!0,delay:5e3};class _o extends Ae{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return go}static get DefaultType(){return mo}static get NAME(){return"toast"}show(){if(ge.trigger(this._element,co).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");this._element.classList.remove(ho),Kt(this._element),this._element.classList.add(fo,po),this._queueCallback((()=>{this._element.classList.remove(po),ge.trigger(this._element,uo),this._maybeScheduleHide()}),this._element,this._config.animation)}hide(){if(!this.isShown())return;if(ge.trigger(this._element,ao).defaultPrevented)return;this._element.classList.add(po),this._queueCallback((()=>{this._element.classList.add(ho),this._element.classList.remove(po,fo),ge.trigger(this._element,lo)}),this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(fo),super.dispose()}isShown(){return this._element.classList.contains(fo)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}if(e)return void this._clearTimeout();const n=t.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}_setListeners(){ge.on(this._element,io,(t=>this._onInteraction(t,!0))),ge.on(this._element,so,(t=>this._onInteraction(t,!1))),ge.on(this._element,oo,(t=>this._onInteraction(t,!0))),ge.on(this._element,ro,(t=>this._onInteraction(t,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each((function(){const e=_o.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}Te(_o),Gt(_o),[].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]')).map((function(t){let e={boundary:"viewport"===t.getAttribute("data-bs-boundary")?document.querySelector(".btn"):"clippingParents"};return new Gn(t,e)})),[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map((function(t){let e={delay:{show:50,hide:50},html:"true"===t.getAttribute("data-bs-html")??!1,placement:t.getAttribute("data-bs-placement")??"auto"};return new gs(t,e)})),[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')).map((function(t){let e={delay:{show:50,hide:50},html:"true"===t.getAttribute("data-bs-html")??!1,placement:t.getAttribute("data-bs-placement")??"auto"};return new ws(t,e)}));n(3794);(()=>{const t=window.location.hash;if(t){[].slice.call(document.querySelectorAll('[data-bs-toggle="tab"]')).filter((e=>e.hash===t)).map((t=>{new eo(t).show()}))}})();const bo="tblr-",vo=(t,e)=>{const n=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return n?`rgba(${parseInt(n[1],16)}, ${parseInt(n[2],16)}, ${parseInt(n[3],16)}, ${e})`:null},yo=(t,e=1)=>{const n=getComputedStyle(document.body).getPropertyValue(`--${bo}${t}`).trim();return 1!==e?vo(n,e):n};var wo=n(5947),Ao=n.n(wo);function Eo(t){var e=document.createElement("style");e.textContent="\n        #nprogress {\n          pointer-events: none;\n        }\n\n        #nprogress .bar {\n          background: ".concat(t,";\n\n          position: fixed;\n          z-index: 1031;\n          top: 0;\n          left: 0;\n\n          width: 100%;\n          height: 2px;\n        }\n\n        #nprogress .peg {\n          display: block;\n          position: absolute;\n          right: 0px;\n          width: 100px;\n          height: 100%;\n          box-shadow: 0 0 10px ").concat(t,", 0 0 5px ").concat(t,";\n          opacity: 1.0;\n\n          -webkit-transform: rotate(3deg) translate(0px, -4px);\n              -ms-transform: rotate(3deg) translate(0px, -4px);\n                  transform: rotate(3deg) translate(0px, -4px);\n        }\n\n        #nprogress .spinner {\n          display: block;\n          position: fixed;\n          z-index: 1031;\n          top: 15px;\n          right: 15px;\n        }\n\n        #nprogress .spinner-icon {\n          width: 18px;\n          height: 18px;\n          box-sizing: border-box;\n\n          border: solid 2px transparent;\n          border-top-color: ").concat(t,";\n          border-left-color: ").concat(t,";\n          border-radius: 50%;\n\n          -webkit-animation: nprogress-spinner 400ms linear infinite;\n                  animation: nprogress-spinner 400ms linear infinite;\n        }\n\n        .nprogress-custom-parent {\n          overflow: hidden;\n          position: relative;\n        }\n\n        .nprogress-custom-parent #nprogress .spinner,\n        .nprogress-custom-parent #nprogress .bar {\n          position: absolute;\n        }\n\n        @-webkit-keyframes nprogress-spinner {\n          0%   { -webkit-transform: rotate(0deg); }\n          100% { -webkit-transform: rotate(360deg); }\n        }\n        @keyframes nprogress-spinner {\n          0%   { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n    "),document.head.appendChild(e)}Eo("#007bff"),globalThis.bootstrap=e,globalThis.tabler=i,function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.delay,n=void 0===e?250:e,i=t.color,s=void 0===i?"var(--bb-primary)":i,o=t.includeCSS,r=void 0===o||o,a=t.showSpinner,l=void 0!==a&&a;$(document).on("ajaxSend",(function(){return Ao().inc(n)})),$(document).on("ajaxStop",(function(){return Ao().done()})),$httpClient.beforeSend((function(){return Ao().inc(n)})),$httpClient.completed((function(){return Ao().done()})),Ao().configure({showSpinner:l}),r&&Eo(s)}({showSpinner:!0})})()})();