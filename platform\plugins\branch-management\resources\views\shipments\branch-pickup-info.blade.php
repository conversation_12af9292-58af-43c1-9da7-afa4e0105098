@if($shipment->order->shippingAddress && $shipment->order->shippingAddress->is_branch_pickup && $shipment->order->shippingAddress->branch)
<x-core::card class="mb-3">
    <x-core::card.header>
        <x-core::card.title>
            <div class="d-flex align-items-center">
                <x-core::icon name="ti ti-building-store" class="me-2 text-primary" />
                {{ trans('plugins/branch-management::branch.pickup.shipment_title') }}
            </div>
        </x-core::card.title>
    </x-core::card.header>
    <x-core::card.body>
        <div class="alert alert-info mb-3">
            <div class="d-flex align-items-center">
                <x-core::icon name="ti ti-info-circle" class="me-2" />
                <strong>{{ trans('plugins/branch-management::branch.pickup.shipment_notice') }}</strong>
            </div>
        </div>

        <x-core::datagrid>
            <x-core::datagrid.item>
                <x-slot:title>
                    {{ trans('plugins/branch-management::branch.pickup.delivery_type') }}
                </x-slot:title>
                <div class="d-flex align-items-center">
                    <x-core::icon name="ti ti-truck-off" class="me-2 text-warning" />
                    <span class="badge bg-warning text-dark">{{ trans('plugins/branch-management::branch.pickup.title') }}</span>
                </div>
            </x-core::datagrid.item>

            <x-core::datagrid.item>
                <x-slot:title>
                    {{ trans('plugins/branch-management::branch.pickup.destination_branch') }}
                </x-slot:title>
                <div class="d-flex align-items-center">
                    <x-core::icon name="ti ti-building" class="me-2 text-muted" />
                    <strong class="text-primary">{{ $shipment->order->shippingAddress->branch->name }}</strong>
                </div>
            </x-core::datagrid.item>

            <x-core::datagrid.item>
                <x-slot:title>
                    {{ trans('plugins/branch-management::branch.address') }}
                </x-slot:title>
                <div class="d-flex align-items-center">
                    <x-core::icon name="ti ti-map-pin" class="me-2 text-muted" />
                    <span>{{ $shipment->order->shippingAddress->branch->full_address }}</span>
                </div>
                @if($shipment->order->shippingAddress->branch->full_address)
                    <div class="mt-2">
                        <a href="https://maps.google.com/?q={{ urlencode($shipment->order->shippingAddress->branch->full_address) }}" 
                           target="_blank" 
                           class="btn btn-sm btn-outline-primary">
                            <x-core::icon name="ti ti-map" class="me-1" />
                            {{ trans('plugins/ecommerce::order.see_on_maps') }}
                        </a>
                    </div>
                @endif
            </x-core::datagrid.item>

            @if($shipment->order->shippingAddress->branch->phone)
                <x-core::datagrid.item>
                    <x-slot:title>
                        {{ trans('plugins/branch-management::branch.contact_phone') }}
                    </x-slot:title>
                    <div class="d-flex align-items-center">
                        <x-core::icon name="ti ti-phone" class="me-2 text-muted" />
                        <a href="tel:{{ $shipment->order->shippingAddress->branch->phone }}" class="text-success text-decoration-none">
                            {{ $shipment->order->shippingAddress->branch->phone }}
                        </a>
                    </div>
                </x-core::datagrid.item>
            @endif

            @if($shipment->order->shippingAddress->branch->manager_name)
                <x-core::datagrid.item>
                    <x-slot:title>
                        {{ trans('plugins/branch-management::branch.branch_manager') }}
                    </x-slot:title>
                    <div class="d-flex align-items-center">
                        <x-core::icon name="ti ti-user" class="me-2 text-muted" />
                        <span>{{ $shipment->order->shippingAddress->branch->manager_name }}</span>
                        @if($shipment->order->shippingAddress->branch->manager_phone)
                            <span class="text-muted ms-2">
                                (<a href="tel:{{ $shipment->order->shippingAddress->branch->manager_phone }}" class="text-decoration-none">{{ $shipment->order->shippingAddress->branch->manager_phone }}</a>)
                            </span>
                        @endif
                    </div>
                </x-core::datagrid.item>
            @endif

            @if($shipment->order->shippingAddress->branch->operating_hours)
                <x-core::datagrid.item>
                    <x-slot:title>
                        {{ trans('plugins/branch-management::branch.operating_hours') }}
                    </x-slot:title>
                    <div class="operating-hours">
                        @php
                            $today = strtolower(now()->format('l'));
                            $todayHours = $shipment->order->shippingAddress->branch->operating_hours[$today] ?? null;
                        @endphp
                        
                        @if($todayHours && isset($todayHours['is_open']) && $todayHours['is_open'])
                            <div class="alert alert-success mb-2 py-2">
                                <strong>{{ trans('plugins/branch-management::branch.today') }}:</strong>
                                {{ $todayHours['open'] ?? '' }} - {{ $todayHours['close'] ?? '' }}
                                @if($shipment->order->shippingAddress->branch->is_open)
                                    <span class="badge bg-success ms-2">{{ trans('plugins/branch-management::branch.open_now') }}</span>
                                @else
                                    <span class="badge bg-danger ms-2">{{ trans('plugins/branch-management::branch.closed_now') }}</span>
                                @endif
                            </div>
                        @else
                            <div class="alert alert-warning mb-2 py-2">
                                <strong>{{ trans('plugins/branch-management::branch.today') }}:</strong>
                                <span class="text-danger">{{ trans('plugins/branch-management::branch.closed') }}</span>
                            </div>
                        @endif
                        
                        <details>
                            <summary class="text-muted" style="cursor: pointer;">{{ trans('plugins/branch-management::branch.view_all_hours') }}</summary>
                            <div class="mt-2">
                                @foreach($shipment->order->shippingAddress->branch->operating_hours as $day => $hours)
                                    <div class="d-flex justify-content-between mb-1">
                                        <span class="text-capitalize @if($day === $today) fw-bold @endif">{{ $day }}:</span>
                                        @if(isset($hours['is_open']) && $hours['is_open'])
                                            <span class="text-muted">{{ $hours['open'] ?? '' }} - {{ $hours['close'] ?? '' }}</span>
                                        @else
                                            <span class="text-danger">{{ trans('plugins/branch-management::branch.closed') }}</span>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </details>
                    </div>
                </x-core::datagrid.item>
            @endif

            @if($shipment->order->shippingAddress->branch->special_instructions)
                <x-core::datagrid.item>
                    <x-slot:title>
                        {{ trans('plugins/branch-management::branch.delivery_instructions') }}
                    </x-slot:title>
                    <div class="alert alert-warning mb-0">
                        <x-core::icon name="ti ti-alert-triangle" class="me-2" />
                        {!! BaseHelper::clean($shipment->order->shippingAddress->branch->special_instructions) !!}
                    </div>
                </x-core::datagrid.item>
            @endif
        </x-core::datagrid>

        <div class="mt-3 p-3 bg-light rounded">
            <h6 class="mb-2">{{ trans('plugins/branch-management::branch.pickup.shipment_instructions') }}</h6>
            <ul class="mb-0 text-muted small">
                <li>{{ trans('plugins/branch-management::branch.pickup.shipment_instruction_1') }}</li>
                <li>{{ trans('plugins/branch-management::branch.pickup.shipment_instruction_2') }}</li>
                <li>{{ trans('plugins/branch-management::branch.pickup.shipment_instruction_3') }}</li>
            </ul>
        </div>
    </x-core::card.body>
</x-core::card>
@endif
