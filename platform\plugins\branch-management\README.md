# Branch Management Plugin for Botble CMS

## Overview

The Branch Management plugin provides a comprehensive solution for managing store branches with pickup options for e-commerce orders. It integrates seamlessly with the existing Location plugin and Ecommerce functionality.

## Features

- **Complete CRUD system** for branch management
- **Integration with Location plugin** (Country, State, City relationships)
- **Branch pickup option** during checkout
- **Operating hours management** with real-time status
- **Manager and contact information** tracking
- **Facilities and special instructions** support
- **Frontend branch display** on product pages
- **Shortcode support** for displaying branches
- **GPS coordinates** support for directions
- **Pickup fee management** per branch
- **Featured branches** system

## Installation

1. **Copy the plugin** to `platform/plugins/branch-management/`
2. **Activate the plugin** through the admin panel or run:
   ```bash
   php artisan cms:plugin:activate branch-management
   ```
3. **Run migrations** (automatically handled during activation)
4. **Configure settings** in Admin Panel > Branch Management > Settings

## Database Schema

### Branches Table
- `id` - Primary key
- `name` - Branch name
- `slug` - URL-friendly identifier
- `description` - Branch description
- `address` - Physical address
- `phone`, `email` - Contact information
- `manager_name`, `manager_phone`, `manager_email` - Manager details
- `city_id` - Foreign key to cities table (required)
- `state_id`, `country_id` - Foreign keys (optional, auto-filled from city)
- `operating_hours` - JSON field for daily hours
- `zip_code` - Postal code
- `latitude`, `longitude` - GPS coordinates
- `image` - Branch image
- `gallery` - JSON array of images
- `facilities` - JSON array of available facilities
- `special_instructions` - Text field for special notes
- `status` - Published/Draft status
- `order` - Sort order
- `is_featured` - Featured branch flag
- `is_pickup_available` - Pickup availability flag
- `pickup_fee` - Fee for pickup service

### Order Address Extensions
- `branch_id` - Foreign key to branches table
- `is_branch_pickup` - Boolean flag for pickup orders

## Configuration

### Plugin Settings
Access via Admin Panel > Settings > Branch Management

- **Enable Branch Pickup**: Allow customers to pick up orders from branches
- **Default Pickup Fee**: Default fee for branch pickup (can be overridden per branch)
- **Show on Product Page**: Display available branches on product detail pages

## Usage

### Admin Panel

1. **Manage Branches**: Admin Panel > Branches
   - Create, edit, delete branches
   - Set operating hours
   - Configure pickup availability and fees
   - Upload branch images
   - Set GPS coordinates

2. **Settings**: Admin Panel > Settings > Branch Management
   - Configure global pickup settings
   - Set default pickup fees
   - Enable/disable features

### Frontend Integration

#### Product Pages
Branches are automatically displayed on product pages when enabled in settings.

#### Checkout Process
When branch pickup is enabled:
1. Customer selects "Branch Pickup" as shipping method
2. Available branches in the selected city are loaded
3. Customer selects specific branch
4. Pickup fee is calculated and added to order

#### Shortcode Usage
Display branches using the shortcode:
```
[branches-list city_id="1" limit="10"]
```

Parameters:
- `city_id` (optional): Show branches for specific city only
- `limit` (optional): Maximum number of branches to display (default: 10)

## API Endpoints

### Public Endpoints
- `GET /ajax/branches-by-city` - Get branches by city ID
- `GET /branches` - List all branches
- `GET /branches/{slug}` - Branch detail page

### Admin Endpoints
- Standard CRUD routes under `/admin/branches`
- Settings routes under `/admin/settings/branch-management`

## Hooks and Filters

### Actions Used
- `ecommerce_checkout_form_after_shipping_method_form` - Add branch pickup option to checkout (positioned after shipping methods)
- `ecommerce_product_detail_extra_html` - Display branches on product pages
- `ecommerce_order_created` - Handle branch pickup orders

### Filters Used
- `BASE_FILTER_ENUM_ARRAY` - Add branch pickup to shipping methods
- `BASE_FILTER_ENUM_LABEL` - Set branch pickup method label
- `handle_shipping_fee` - Calculate pickup fees
- `ecommerce_shipping_methods` - Add branch pickup methods

### Custom Actions Fired
- `BRANCH_MANAGEMENT_ACTION_AFTER_CREATE` - After branch creation
- `BRANCH_MANAGEMENT_ACTION_AFTER_UPDATE` - After branch update
- `BRANCH_MANAGEMENT_ACTION_AFTER_DELETE` - After branch deletion

## Helper Functions

- `branch_management_get_branches_by_city($cityId, $pickupOnly = false)`
- `branch_management_get_featured_branches($limit = 10)`
- `branch_management_get_pickup_available_branches()`
- `branch_management_get_branches_grouped_by_city()`
- `branch_management_is_pickup_enabled()`
- `branch_management_get_branch_by_id($branchId)`

## Dependencies

- **Required**: Location plugin (for Country, State, City models)
- **Required**: Ecommerce plugin (for checkout integration)
- **Optional**: Shortcode plugin (for shortcode functionality)

## Permissions

- `branch-management.index` - Access branch management
- `branches.index` - View branches list
- `branches.create` - Create branches
- `branches.edit` - Edit branches
- `branches.destroy` - Delete branches
- `branch-management.settings` - Manage settings

## Custom Code Integration

### Required Theme Modifications

The plugin uses hooks to integrate with the checkout and product pages. However, if the hooks are not available in your theme, you may need to add the following code manually:

#### 1. Checkout Page Integration

**File**: `platform/themes/martfury/views/ecommerce/orders/checkout.blade.php`

Add this code before the shipping methods section:

```php
@if(is_plugin_active('branch-management'))
    @include('plugins/branch-management::checkout.branch-pickup-option')
@endif
```

#### 2. Product Page Integration

**File**: `platform/themes/martfury/views/ecommerce/product.blade.php`

Add this code in the product details section:

```php
@if(is_plugin_active('branch-management'))
    @include('plugins/branch-management::product.branches', ['product' => $product])
@endif
```

#### 3. Order Details Integration (Admin)

**File**: `platform/plugins/ecommerce/resources/views/orders/show.blade.php`

Add this code in the shipping address section:

```php
@if(is_plugin_active('branch-management'))
    @include('plugins/branch-management::orders.branch-pickup-info', ['order' => $order])
@endif
```

#### 4. Order Details Integration (Frontend)

**File**: `platform/themes/martfury/views/ecommerce/orders/detail.blade.php`

Add this code in the order details section:

```php
@if(is_plugin_active('branch-management') && $order->shippingAddress->is_branch_pickup)
    <div class="order-branch-info">
        <h5>{{ trans('plugins/branch-management::branch.pickup.title') }}</h5>
        <p><strong>{{ $order->shippingAddress->branch_name }}</strong></p>
        <p>{{ $order->shippingAddress->branch_full_address }}</p>
        @if($order->shippingAddress->branch_phone)
            <p>{{ __('Phone') }}: {{ $order->shippingAddress->branch_phone }}</p>
        @endif
    </div>
@endif
```

### JavaScript Integration

#### Checkout Form Enhancement

**File**: `platform/themes/martfury/assets/js/checkout.js`

Add this code to handle branch selection:

```javascript
// Handle branch pickup method selection
$(document).on('change', 'input[name="shipping_method"]', function() {
    const branchPickupSection = $('#branch-pickup-section');
    if ($(this).val() === 'branch_pickup') {
        branchPickupSection.show();
        loadBranchesByCity();
    } else {
        branchPickupSection.hide();
    }
});

// Load branches when city changes
$(document).on('change', 'select[name="address[city]"]', function() {
    if ($('input[name="shipping_method"]:checked').val() === 'branch_pickup') {
        loadBranchesByCity();
    }
});

function loadBranchesByCity() {
    const cityId = $('select[name="address[city]"]').val();
    const branchSelect = $('#pickup_branch_id');

    if (!cityId) return;

    $.get('/ajax/branches-by-city', { city_id: cityId, pickup_only: 1 })
        .done(function(branches) {
            branchSelect.empty().append('<option value="">Select a branch...</option>');
            branches.forEach(function(branch) {
                branchSelect.append(`<option value="${branch.id}" data-branch='${JSON.stringify(branch)}'>${branch.name} - ${branch.address}</option>`);
            });
        });
}
```

## Post-Update Instructions

After each software update, ensure the following:

### 1. Check Plugin Status
- Verify the plugin is still activated
- Check that all database tables exist
- Confirm settings are preserved

### 2. Re-apply Theme Modifications
If you manually added code to theme files, you may need to re-apply these changes after theme updates:

- Checkout page branch pickup integration
- Product page branch display
- Order details branch information

### 3. Clear Cache
```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

### 4. Verify Functionality
- Test branch creation/editing in admin
- Test checkout with branch pickup
- Verify branch display on product pages
- Check order details show branch information

## Troubleshooting

### Common Issues

1. **Branches not showing in checkout**
   - Ensure Location plugin is active
   - Check that branches exist for the selected city
   - Verify pickup is enabled in settings

2. **Database errors**
   - Run migrations: `php artisan migrate`
   - Check Location plugin is installed first

3. **Missing branch information in orders**
   - Verify the order address migration ran successfully
   - Check that the branch pickup option was selected during checkout

### Support

For support and customization requests, contact the development team.
